#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: "3.8"

services:
  dolphinscheduler:
    image: apache/dolphinscheduler-standalone-server:ci
    environment:
      MASTER_MAX_CPU_LOAD_AVG: 100
      WORKER_TENANT_AUTO_CREATE: 'true'
    ports:
      - "12345:12345"
    networks:
      - api-test
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:12345/actuator/health" ]
      interval: 5s
      timeout: 60s
      retries: 120
    depends_on:
      hive-server:
        condition: service_healthy
  namenode:
    image: bde2020/hadoop-namenode:2.0.0-hadoop2.7.4-java8
    environment:
      - CLUSTER_NAME=test
    env_file:
      - ./hadoop-hive.env
    networks:
      - e2e
    expose:
      - "50070"
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:50070/" ]
      interval: 5s
      timeout: 60s
      retries: 120
  datanode:
    image: bde2020/hadoop-datanode:2.0.0-hadoop2.7.4-java8
    env_file:
      - ./hadoop-hive.env
    environment:
      SERVICE_PRECONDITION: "namenode:50070"
    networks:
      - e2e
    expose:
      - "50075"
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:50075" ]
      interval: 5s
      timeout: 60s
      retries: 120
  hive-server:
    image: bde2020/hive:2.3.2-postgresql-metastore
    env_file:
      - ./hadoop-hive.env
    networks:
      - e2e
    environment:
      HIVE_CORE_CONF_javax_jdo_option_ConnectionURL: "******************************************"
      SERVICE_PRECONDITION: "hive-metastore:9083"
    expose:
      - "10000"
    depends_on:
      datanode:
        condition: service_healthy
      namenode:
        condition: service_healthy
    healthcheck:
      test: beeline -u "************************************" -n health_check -e "show databases;"
      interval: 5s
      timeout: 60s
      retries: 120
  hive-metastore:
    image: bde2020/hive:2.3.2-postgresql-metastore
    env_file:
      - ./hadoop-hive.env
    command: /opt/hive/bin/hive --service metastore
    networks:
      - e2e
    environment:
      SERVICE_PRECONDITION: "namenode:50070 datanode:50075 hive-metastore-postgresql:5432"
    expose:
      - "9083"
    depends_on:
      hive-metastore-postgresql:
        condition: service_healthy
  hive-metastore-postgresql:
    image: bde2020/hive-metastore-postgresql:2.3.0
    networks:
      - e2e
    expose:
      - "5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 60s
      retries: 120


networks:
  api-test:
