#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: "3.8"

services:
  dolphinscheduler:
    image: apache/dolphinscheduler-standalone-server:ci
    environment:
      MASTER_MAX_CPU_LOAD_AVG: 100
      WORKER_TENANT_AUTO_CREATE: 'true'
    ports:
      - "12345:12345"
    networks:
      - api-test
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:12345/actuator/health" ]
      interval: 5s
      timeout: 60s
      retries: 120
    depends_on:
      postgres:
        condition: service_healthy
  postgres:
    image: postgres:14.1
    restart: always
    environment:
      POSTGRES_PASSWORD: postgres
    networks:
      - e2e
    expose:
      - 5432
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 60s
      retries: 120

networks:
  api-test:
