-- MySQL dump 10.13  Distrib 8.0.30, for Win64 (x86_64)
--
-- Host: **************    Database: business_dolphinscheduler
-- ------------------------------------------------------
-- Server version	8.0.27

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `qrtz_blob_triggers`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `BLOB_DATA` blob,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  KEY `SCHED_NAME` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_BLOB_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_blob_triggers_copy1`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers_copy1`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers_copy1` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `BLOB_DATA` blob,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  KEY `SCHED_NAME` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_copy1_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_calendars`
--

DROP TABLE IF EXISTS `qrtz_calendars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_calendars` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CALENDAR_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CALENDAR` blob NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`CALENDAR_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;


-- Table structure for table `qrtz_cron_triggers`
--

DROP TABLE IF EXISTS `qrtz_cron_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_cron_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CRON_EXPRESSION` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TIME_ZONE_ID` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_CRON_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_fired_triggers`
--

DROP TABLE IF EXISTS `qrtz_fired_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_fired_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ENTRY_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `FIRED_TIME` bigint NOT NULL,
  `SCHED_TIME` bigint NOT NULL,
  `PRIORITY` int NOT NULL,
  `STATE` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`ENTRY_ID`) USING BTREE,
  KEY `IDX_QRTZ_FT_TRIG_INST_NAME` (`SCHED_NAME`,`INSTANCE_NAME`) USING BTREE,
  KEY `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY` (`SCHED_NAME`,`INSTANCE_NAME`,`REQUESTS_RECOVERY`) USING BTREE,
  KEY `IDX_QRTZ_FT_J_G` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_FT_JG` (`SCHED_NAME`,`JOB_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_FT_T_G` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_FT_TG` (`SCHED_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_job_details`
--

DROP TABLE IF EXISTS `qrtz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_job_details` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `JOB_CLASS_NAME` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_DURABLE` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_UPDATE_DATA` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_DATA` blob,
  PRIMARY KEY (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_J_REQ_RECOVERY` (`SCHED_NAME`,`REQUESTS_RECOVERY`) USING BTREE,
  KEY `IDX_QRTZ_J_GRP` (`SCHED_NAME`,`JOB_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_locks`
--

DROP TABLE IF EXISTS `qrtz_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_locks` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `LOCK_NAME` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`LOCK_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

INSERT INTO `qrtz_locks` VALUES ('DolphinScheduler', 'STATE_ACCESS');
INSERT INTO `qrtz_locks` VALUES ('DolphinScheduler', 'TRIGGER_ACCESS');
--
-- Table structure for table `qrtz_paused_trigger_grps`
--

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_GROUP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_scheduler_state`
--

DROP TABLE IF EXISTS `qrtz_scheduler_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_scheduler_state` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `LAST_CHECKIN_TIME` bigint NOT NULL,
  `CHECKIN_INTERVAL` bigint NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`INSTANCE_NAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_simple_triggers`
--

DROP TABLE IF EXISTS `qrtz_simple_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simple_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `REPEAT_COUNT` bigint NOT NULL,
  `REPEAT_INTERVAL` bigint NOT NULL,
  `TIMES_TRIGGERED` bigint NOT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_simprop_triggers`
--

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simprop_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `STR_PROP_1` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `STR_PROP_2` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `STR_PROP_3` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `INT_PROP_1` int DEFAULT NULL,
  `INT_PROP_2` int DEFAULT NULL,
  `LONG_PROP_1` bigint DEFAULT NULL,
  `LONG_PROP_2` bigint DEFAULT NULL,
  `DEC_PROP_1` decimal(13,4) DEFAULT NULL,
  `DEC_PROP_2` decimal(13,4) DEFAULT NULL,
  `BOOL_PROP_1` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `BOOL_PROP_2` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `qrtz_triggers` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qrtz_triggers`
--

DROP TABLE IF EXISTS `qrtz_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_triggers` (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `NEXT_FIRE_TIME` bigint DEFAULT NULL,
  `PREV_FIRE_TIME` bigint DEFAULT NULL,
  `PRIORITY` int DEFAULT NULL,
  `TRIGGER_STATE` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_TYPE` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `START_TIME` bigint NOT NULL,
  `END_TIME` bigint DEFAULT NULL,
  `CALENDAR_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `MISFIRE_INSTR` smallint DEFAULT NULL,
  `JOB_DATA` blob,
  PRIMARY KEY (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_T_J` (`SCHED_NAME`,`JOB_NAME`,`JOB_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_T_JG` (`SCHED_NAME`,`JOB_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_T_C` (`SCHED_NAME`,`CALENDAR_NAME`) USING BTREE,
  KEY `IDX_QRTZ_T_G` (`SCHED_NAME`,`TRIGGER_GROUP`) USING BTREE,
  KEY `IDX_QRTZ_T_STATE` (`SCHED_NAME`,`TRIGGER_STATE`) USING BTREE,
  KEY `IDX_QRTZ_T_N_STATE` (`SCHED_NAME`,`TRIGGER_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`) USING BTREE,
  KEY `IDX_QRTZ_T_N_G_STATE` (`SCHED_NAME`,`TRIGGER_GROUP`,`TRIGGER_STATE`) USING BTREE,
  KEY `IDX_QRTZ_T_NEXT_FIRE_TIME` (`SCHED_NAME`,`NEXT_FIRE_TIME`) USING BTREE,
  KEY `IDX_QRTZ_T_NFT_ST` (`SCHED_NAME`,`TRIGGER_STATE`,`NEXT_FIRE_TIME`) USING BTREE,
  KEY `IDX_QRTZ_T_NFT_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`) USING BTREE,
  KEY `IDX_QRTZ_T_NFT_ST_MISFIRE` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_STATE`) USING BTREE,
  KEY `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP` (`SCHED_NAME`,`MISFIRE_INSTR`,`NEXT_FIRE_TIME`,`TRIGGER_GROUP`,`TRIGGER_STATE`) USING BTREE,
  CONSTRAINT `QRTZ_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `qrtz_job_details` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shedlock`
--

-- ----------------------------
-- Table structure for t_ds_catalog
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_catalog`;
CREATE TABLE `t_ds_catalog`  (
                                 `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
                                 `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目录名',
                                 `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级id',
                                 `level` int(0) NULL DEFAULT NULL COMMENT '层级',
                                 `parent_names` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级名称 以中横线分割（含自己）',
                                 `parent_ids` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级id 以中横线分割 （含自己）',
                                 `project_code` bigint(0) NULL DEFAULT NULL COMMENT 'project Id',
                                 `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
                                 `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'creator id',
                                 `description` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                 `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
                                 `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_catalog
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_catalog_process_definition_relation
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_catalog_process_definition_relation`;
CREATE TABLE `t_ds_catalog_process_definition_relation`  (
                                                             `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
                                                             `catalog_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目录id',
                                                             `process_definition_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作流code',
                                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
                                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
                                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `shedlock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shedlock` (
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '锁名称',
  `lock_until` datetime DEFAULT NULL COMMENT '释放锁时间',
  `locked_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '获取锁时间',
  `locked_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '主机名称',
  PRIMARY KEY (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='定时任务锁';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_access_token`
--

DROP TABLE IF EXISTS `t_ds_access_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_access_token` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'user id',
  `token` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'token',
  `expire_time` datetime DEFAULT NULL COMMENT 'end time of token ',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_alert`
--

DROP TABLE IF EXISTS `t_ds_alert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_alert` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'title',
  `sign` char(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'sign=sha1(content)',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'Message content (can be email, can be SMS. Mail is stored in JSON map, and SMS is string)',
  `alert_status` tinyint DEFAULT '0' COMMENT '0:wait running,1:success,2:failed',
  `warning_type` tinyint DEFAULT '2' COMMENT '1 process is successfully, 2 process/task is failed',
  `log` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'log',
  `alertgroup_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert group id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `project_code` bigint DEFAULT NULL COMMENT 'project_code',
  `process_definition_code` bigint DEFAULT NULL COMMENT 'process_definition_code',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process_instance_id',
  `alert_type` int DEFAULT NULL COMMENT 'alert_type',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_status` (`alert_status`) USING BTREE,
  KEY `idx_sign` (`sign`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=176 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_alert_plugin_instance`
--

DROP TABLE IF EXISTS `t_ds_alert_plugin_instance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_alert_plugin_instance` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_define_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_instance_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'plugin instance params. Also contain the params value which user input in web ui.',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `instance_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert instance name',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_alert_send_status`
--

DROP TABLE IF EXISTS `t_ds_alert_send_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_alert_send_status` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_plugin_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `send_status` tinyint DEFAULT '0',
  `log` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `alert_send_status_unique` (`alert_id`,`alert_plugin_instance_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_alertgroup`
--

DROP TABLE IF EXISTS `t_ds_alertgroup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_alertgroup` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_instance_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert instance ids',
  `create_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'create user id',
  `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'group name',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `t_ds_alertgroup_name_un` (`group_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_audit_log`
--

DROP TABLE IF EXISTS `t_ds_audit_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_audit_log` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `resource_type` int NOT NULL COMMENT 'resource type',
  `operation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'operation',
  `time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `resource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'resource id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_cluster`
--

DROP TABLE IF EXISTS `t_ds_cluster`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_cluster` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint DEFAULT NULL COMMENT 'encoding',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'cluster name',
  `config` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'this config contains many cluster variables config',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'the details',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cluster_name_unique` (`name`) USING BTREE,
  UNIQUE KEY `cluster_code_unique` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_command`
--

DROP TABLE IF EXISTS `t_ds_command`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_command` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `command_type` tinyint DEFAULT NULL COMMENT 'Command type: 0 start workflow, 1 start execution from current node, 2 resume fault-tolerant workflow, 3 resume pause process, 4 start execution from failed node, 5 complement, 6 schedule, 7 rerun, 8 pause, 9 stop, 10 resume waiting thread',
  `process_definition_code` bigint NOT NULL COMMENT 'process definition code',
  `process_definition_version` int DEFAULT '0' COMMENT 'process definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT 'process instance id',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'json command parameters',
  `task_depend_type` tinyint DEFAULT NULL COMMENT 'Node dependency type: 0 current node, 1 forward, 2 backward',
  `failure_strategy` tinyint DEFAULT '0' COMMENT 'Failed policy: 0 end, 1 continue',
  `warning_type` tinyint DEFAULT '0' COMMENT 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'warning group',
  `schedule_time` datetime DEFAULT NULL COMMENT 'schedule time',
  `start_time` datetime DEFAULT NULL COMMENT 'start time',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'executor id',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `process_instance_priority` int DEFAULT '2' COMMENT 'process instance priority: 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker group',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `dry_run` tinyint DEFAULT '0' COMMENT 'dry run flag：0 normal, 1 dry run',
  `test_flag` tinyint DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tenant_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `priority_id_index` (`process_instance_priority`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_datasource`
--

DROP TABLE IF EXISTS `t_ds_datasource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_datasource` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'data source name',
  `note` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'description',
  `type` tinyint NOT NULL COMMENT 'data source type: 0:mysql,1:postgresql,2:hive,3:spark',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the creator id',
  `connection_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'json connection params',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `t_ds_datasource_name_un` (`name`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_comparison_type`
--

DROP TABLE IF EXISTS `t_ds_dq_comparison_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_comparison_type` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `execute_sql` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `output_table` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `is_inner_source` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_execute_result`
--

DROP TABLE IF EXISTS `t_ds_dq_execute_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_execute_result` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule_type` int DEFAULT NULL,
  `rule_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `statistics_value` double DEFAULT NULL,
  `comparison_value` double DEFAULT NULL,
  `check_type` int DEFAULT NULL,
  `threshold` double DEFAULT NULL,
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `failure_strategy` int DEFAULT NULL,
  `state` int DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `comparison_type` int DEFAULT NULL,
  `error_output_path` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_rule`
--

DROP TABLE IF EXISTS `t_ds_dq_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_rule` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_rule_execute_sql`
--

DROP TABLE IF EXISTS `t_ds_dq_rule_execute_sql`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_rule_execute_sql` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `index` int DEFAULT NULL,
  `sql` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `table_alias` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` int DEFAULT NULL,
  `is_error_output_sql` tinyint(1) DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_rule_input_entry`
--

DROP TABLE IF EXISTS `t_ds_dq_rule_input_entry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_rule_input_entry` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `field` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `options` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `placeholder` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `option_source_type` int DEFAULT NULL,
  `value_type` int DEFAULT NULL,
  `input_type` int DEFAULT NULL,
  `is_show` tinyint(1) DEFAULT '1',
  `can_edit` tinyint(1) DEFAULT '1',
  `is_emit` tinyint(1) DEFAULT '0',
  `is_validate` tinyint(1) DEFAULT '1',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_dq_task_statistics_value`
--

DROP TABLE IF EXISTS `t_ds_dq_task_statistics_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_dq_task_statistics_value` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `unique_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `statistics_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `statistics_value` double DEFAULT NULL,
  `data_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_environment`
--

DROP TABLE IF EXISTS `t_ds_environment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_environment` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint DEFAULT NULL COMMENT 'encoding',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'environment name',
  `config` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'this config contains many environment variables config',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'the details',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `environment_name_unique` (`name`) USING BTREE,
  UNIQUE KEY `environment_code_unique` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_environment_worker_group_relation`
--

DROP TABLE IF EXISTS `t_ds_environment_worker_group_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_environment_worker_group_relation` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `environment_code` bigint NOT NULL COMMENT 'environment code',
  `worker_group` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'worker group id',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `environment_worker_group_unique` (`environment_code`,`worker_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

INSERT INTO `t_ds_environment_worker_group_relation` VALUES ('7901586607552', 7901586592576, 'default', '1', '2022-12-16 11:32:25', '2022-12-16 11:32:25');
--
-- Table structure for table `t_ds_error_command`
--

DROP TABLE IF EXISTS `t_ds_error_command`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_error_command` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `command_type` tinyint DEFAULT NULL COMMENT 'command type',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'executor id',
  `process_definition_code` bigint NOT NULL COMMENT 'process definition code',
  `process_definition_version` int DEFAULT '0' COMMENT 'process definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT 'process instance id: 0',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'json command parameters',
  `task_depend_type` tinyint DEFAULT NULL COMMENT 'task depend type',
  `failure_strategy` tinyint DEFAULT '0' COMMENT 'failure strategy',
  `warning_type` tinyint DEFAULT '0' COMMENT 'warning type',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'warning group id',
  `schedule_time` datetime DEFAULT NULL COMMENT 'scheduler time',
  `start_time` datetime DEFAULT NULL COMMENT 'start time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `process_instance_priority` int DEFAULT '2' COMMENT 'process instance priority, 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker group',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `message` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'message',
  `dry_run` tinyint DEFAULT '0' COMMENT 'dry run flag: 0 normal, 1 dry run',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_fav_task`
--

DROP TABLE IF EXISTS `t_ds_fav_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_fav_task` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `task_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'favorite task name',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_k8s`
--

DROP TABLE IF EXISTS `t_ds_k8s`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_k8s` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `k8s_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `k8s_config` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_k8s_namespace`
--

DROP TABLE IF EXISTS `t_ds_k8s_namespace`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_k8s_namespace` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint NOT NULL DEFAULT '0',
  `limits_memory` int DEFAULT NULL,
  `namespace` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pod_replicas` int DEFAULT NULL,
  `pod_request_cpu` decimal(14,3) DEFAULT NULL,
  `pod_request_memory` int DEFAULT NULL,
  `limits_cpu` decimal(14,3) DEFAULT NULL,
  `cluster_code` bigint NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `k8s_namespace_unique` (`namespace`,`cluster_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_mysql_registry_data`
--

DROP TABLE IF EXISTS `t_ds_mysql_registry_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_mysql_registry_data` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `key` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'key, like zookeeper node path',
  `data` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'data, like zookeeper node value',
  `type` tinyint NOT NULL COMMENT '1: ephemeral node, 2: persistent node',
  `last_update_time` timestamp NULL DEFAULT NULL COMMENT 'last update time',
  `create_time` timestamp NULL DEFAULT NULL COMMENT 'create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `key` (`key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_mysql_registry_lock`
--

DROP TABLE IF EXISTS `t_ds_mysql_registry_lock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_mysql_registry_lock` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `key` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'lock path',
  `lock_owner` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the lock owner, ip_processId',
  `last_term` timestamp NOT NULL COMMENT 'last term time',
  `last_update_time` timestamp NULL DEFAULT NULL COMMENT 'last update time',
  `create_time` timestamp NULL DEFAULT NULL COMMENT 'lock create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `key` (`key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_plugin_define`
--

DROP TABLE IF EXISTS `t_ds_plugin_define`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_plugin_define` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the name of plugin eg: email',
  `plugin_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'plugin type . alert=alert plugin, job=job plugin',
  `plugin_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'plugin params',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `t_ds_plugin_define_UN` (`plugin_name`,`plugin_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

INSERT INTO `t_ds_plugin_define` VALUES ('10', 'SHELL', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:47:58', '2022-10-13 10:47:58');
INSERT INTO `t_ds_plugin_define` VALUES ('11', 'DEPENDENT', 'task', 'null', '2022-10-13 10:47:58', '2022-10-13 10:47:58');
INSERT INTO `t_ds_plugin_define` VALUES ('12', 'MR', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('13', 'SQOOP', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('14', 'SUB_PROCESS', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('15', 'PYTORCH', 'task', '[]', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('16', 'K8S', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('17', 'SEATUNNEL', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('18', 'SAGEMAKER', 'task', '[]', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('19', 'HTTP', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('2', 'JUPYTER', 'task', 'null', '2022-10-13 10:47:56', '2022-10-13 10:47:56');
INSERT INTO `t_ds_plugin_define` VALUES ('20', 'EMR', 'task', '[]', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('21', 'DATA_QUALITY', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('22', 'SQL', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('23', 'DVC', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('24', 'DATAX', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('25', 'ZEPPELIN', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('26', 'DINKY', 'task', '[]', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('27', 'MLFLOW', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('28', 'SWITCH', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('29', 'OPENMLDB', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('3', 'SPARK', 'task', 'null', '2022-10-13 10:47:56', '2022-10-13 10:47:56');
INSERT INTO `t_ds_plugin_define` VALUES ('30', 'BLOCKING', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('31', 'FLINK', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('32', 'HIVECLI', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('33', 'STX', 'task', 'null', '2024-02-20 10:42:02', '2024-02-20 10:42:05');
INSERT INTO `t_ds_plugin_define` VALUES ('4', 'FLINK_STREAM', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('5', 'PYTHON', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('6', 'CHUNJUN', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('7', 'CONDITIONS', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662198464', 'Script', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please enter your custom parameters, which will be passed to you when calling your script\",\"size\":\"small\"},\"field\":\"userParams\",\"name\":\"$t(\'userParams\')\",\"type\":\"input\",\"title\":\"$t(\'userParams\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please upload the file to the disk directory of the alert server, and ensure that the path is absolute and has the corresponding access rights\",\"size\":\"small\"},\"field\":\"path\",\"name\":\"$t(\'scriptPath\')\",\"type\":\"input\",\"title\":\"$t(\'scriptPath\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"type\",\"name\":\"$t(\'scriptType\')\",\"type\":\"radio\",\"title\":\"$t(\'scriptType\')\",\"value\":\"SHELL\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"SHELL\",\"value\":\"SHELL\",\"disabled\":false}]}]', '2022-12-09 16:56:38', '2022-12-09 16:56:38');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662217792', 'WeChat', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input corp id \",\"size\":\"small\"},\"field\":\"corpId\",\"name\":\"$t(\'corpId\')\",\"type\":\"input\",\"title\":\"$t(\'corpId\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input secret \",\"size\":\"small\"},\"field\":\"secret\",\"name\":\"$t(\'secret\')\",\"type\":\"input\",\"title\":\"$t(\'secret\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"use `|` to separate userIds and `@all` to everyone \",\"size\":\"small\"},\"field\":\"users\",\"name\":\"$t(\'users\')\",\"type\":\"input\",\"title\":\"$t(\'users\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input agent id or chat id \",\"size\":\"small\"},\"field\":\"agentId/chatId\",\"name\":\"$t(\'agentId/chatId\')\",\"type\":\"input\",\"title\":\"$t(\'agentId/chatId\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"sendType\",\"name\":\"send.type\",\"type\":\"radio\",\"title\":\"send.type\",\"value\":\"APP/应用\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"APP/应用\",\"value\":\"APP/应用\",\"disabled\":false},{\"label\":\"GROUP CHAT/群聊\",\"value\":\"GROUP CHAT/群聊\",\"disabled\":false}]},{\"props\":null,\"field\":\"showType\",\"name\":\"$t(\'showType\')\",\"type\":\"radio\",\"title\":\"$t(\'showType\')\",\"value\":\"markdown\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"markdown\",\"value\":\"markdown\",\"disabled\":false},{\"label\":\"text\",\"value\":\"text\",\"disabled\":false}]}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662238912', 'Telegram', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram web hook\",\"size\":\"small\"},\"field\":\"webHook\",\"name\":\"$t(\'webHook\')\",\"type\":\"input\",\"title\":\"$t(\'webHook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram bot token\",\"size\":\"small\"},\"field\":\"botToken\",\"name\":\"botToken\",\"type\":\"input\",\"title\":\"botToken\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram channel chat id\",\"size\":\"small\"},\"field\":\"chatId\",\"name\":\"chatId\",\"type\":\"input\",\"title\":\"chatId\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"parseMode\",\"name\":\"parseMode\",\"props\":{\"disabled\":null,\"placeholder\":null,\"size\":\"small\"},\"type\":\"select\",\"title\":\"parseMode\",\"value\":\"Txt\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"Txt\",\"value\":\"Txt\",\"disabled\":false},{\"label\":\"Markdown\",\"value\":\"Markdown\",\"disabled\":false},{\"label\":\"MarkdownV2\",\"value\":\"MarkdownV2\",\"disabled\":false},{\"label\":\"Html\",\"value\":\"Html\",\"disabled\":false}]},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662254912', 'Email', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input receives\",\"size\":\"small\"},\"field\":\"receivers\",\"name\":\"$t(\'receivers\')\",\"type\":\"input\",\"title\":\"$t(\'receivers\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"receiverCcs\",\"name\":\"$t(\'receiverCcs\')\",\"type\":\"input\",\"title\":\"$t(\'receiverCcs\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"props\":null,\"field\":\"serverHost\",\"name\":\"mail.smtp.host\",\"type\":\"input\",\"title\":\"mail.smtp.host\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"serverPort\",\"name\":\"mail.smtp.port\",\"type\":\"input\",\"title\":\"mail.smtp.port\",\"value\":\"25\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"sender\",\"name\":\"$t(\'mailSender\')\",\"type\":\"input\",\"title\":\"$t(\'mailSender\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"enableSmtpAuth\",\"name\":\"mail.smtp.auth\",\"type\":\"radio\",\"title\":\"mail.smtp.auth\",\"value\":\"true\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"if enable use authentication, you need input user\",\"size\":\"small\"},\"field\":\"User\",\"name\":\"$t(\'mailUser\')\",\"type\":\"input\",\"title\":\"$t(\'mailUser\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'mailPasswd\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'mailPasswd\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"props\":null,\"field\":\"starttlsEnable\",\"name\":\"mail.smtp.starttls.enable\",\"type\":\"radio\",\"title\":\"mail.smtp.starttls.enable\",\"value\":\"false\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"sslEnable\",\"name\":\"mail.smtp.ssl.enable\",\"type\":\"radio\",\"title\":\"mail.smtp.ssl.enable\",\"value\":\"false\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"smtpSslTrust\",\"name\":\"mail.smtp.ssl.trust\",\"type\":\"input\",\"title\":\"mail.smtp.ssl.trust\",\"value\":\"*\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"showType\",\"name\":\"$t(\'showType\')\",\"type\":\"radio\",\"title\":\"$t(\'showType\')\",\"value\":\"table\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"table\",\"value\":\"table\",\"disabled\":false},{\"label\":\"text\",\"value\":\"text\",\"disabled\":false},{\"label\":\"attachment\",\"value\":\"attachment\",\"disabled\":false},{\"label\":\"table attachment\",\"value\":\"table attachment\",\"disabled\":false}]}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662279488', 'Slack', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Input WebHook Url\",\"size\":\"small\"},\"field\":\"webHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Input the bot username\",\"size\":\"small\"},\"field\":\"username\",\"name\":\"$t(\'Username\')\",\"type\":\"input\",\"title\":\"$t(\'Username\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662320960', 'Feishu', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"WebHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"true\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":null,\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662341824', 'Http', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request URL\",\"size\":\"small\"},\"field\":\"url\",\"name\":\"$t(\'url\')\",\"type\":\"input\",\"title\":\"$t(\'url\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request type POST or GET\",\"size\":\"small\"},\"field\":\"requestType\",\"name\":\"$t(\'requestType\')\",\"type\":\"input\",\"title\":\"$t(\'requestType\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request headers as JSON format \",\"size\":\"small\"},\"field\":\"headerParams\",\"name\":\"$t(\'headerParams\')\",\"type\":\"input\",\"title\":\"$t(\'headerParams\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request body as JSON format \",\"size\":\"small\"},\"field\":\"bodyParams\",\"name\":\"$t(\'bodyParams\')\",\"type\":\"input\",\"title\":\"$t(\'bodyParams\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input alert msg field name\",\"size\":\"small\"},\"field\":\"contentField\",\"name\":\"$t(\'contentField\')\",\"type\":\"input\",\"title\":\"$t(\'contentField\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662360768', 'DingTalk', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"WebHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Keyword\",\"name\":\"$t(\'keyword\')\",\"type\":\"input\",\"title\":\"$t(\'keyword\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Secret\",\"name\":\"$t(\'secret\')\",\"type\":\"input\",\"title\":\"$t(\'secret\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"MsgType\",\"name\":\"$t(\'msgType\')\",\"type\":\"radio\",\"title\":\"$t(\'msgType\')\",\"value\":\"text\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"text\",\"value\":\"text\",\"disabled\":false},{\"label\":\"markdown\",\"value\":\"markdown\",\"disabled\":false}]},{\"props\":null,\"field\":\"AtMobiles\",\"name\":\"$t(\'atMobiles\')\",\"type\":\"input\",\"title\":\"$t(\'atMobiles\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"AtUserIds\",\"name\":\"$t(\'atUserIds\')\",\"type\":\"input\",\"title\":\"$t(\'atUserIds\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"IsAtAll\",\"name\":\"$t(\'isAtAll\')\",\"type\":\"radio\",\"title\":\"$t(\'isAtAll\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":null,\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662381120', 'WebexTeams', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Please enter the robot\'s access token you were given\",\"size\":\"small\"},\"field\":\"BotAccessToken\",\"name\":\"botAccessToken\",\"type\":\"input\",\"title\":\"botAccessToken\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The room ID of the message\",\"size\":\"small\"},\"field\":\"RoomId\",\"name\":\"roomId\",\"type\":\"input\",\"title\":\"roomId\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The person ID of the message recipient\",\"size\":\"small\"},\"field\":\"ToPersonId\",\"name\":\"toPersonId\",\"type\":\"input\",\"title\":\"toPersonId\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The email address of the message recipient\",\"size\":\"small\"},\"field\":\"ToPersonEmail\",\"name\":\"toPersonEmail\",\"type\":\"input\",\"title\":\"toPersonEmail\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"use ,(eng commas) to separate multiple emails\",\"size\":\"small\"},\"field\":\"AtSomeoneInRoom\",\"name\":\"atSomeoneInRoom\",\"type\":\"input\",\"title\":\"atSomeoneInRoom\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Destination\",\"name\":\"destination\",\"type\":\"radio\",\"title\":\"destination\",\"value\":\"roomId\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"roomId\",\"value\":\"roomId\",\"disabled\":false},{\"label\":\"personEmail\",\"value\":\"personEmail\",\"disabled\":false},{\"label\":\"personId\",\"value\":\"personId\",\"disabled\":false}]}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662402752', 'PagerDuty', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"IntegrationKey\",\"name\":\"integrationKey\",\"type\":\"input\",\"title\":\"integrationKey\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('8', 'PIGEON', 'task', '[{\"props\":null,\"field\":\"targetJobName\",\"name\":\"targetJobName\",\"type\":\"input\",\"title\":\"targetJobName\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('9', 'PROCEDURE', 'task', 'null', '2022-10-13 10:47:58', '2022-10-13 10:47:58');


--
-- Table structure for table `t_ds_process_definition`
--

DROP TABLE IF EXISTS `t_ds_process_definition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_process_definition` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint NOT NULL COMMENT 'encoding',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process definition name',
  `version` int DEFAULT '0' COMMENT 'process definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'description',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `release_state` tinyint DEFAULT NULL COMMENT 'process definition release state：0:offline,1:online',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process definition creator id',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'global parameters',
  `flag` tinyint DEFAULT NULL COMMENT '0 not available, 1 available',
  `locations` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'Node location information',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert group id',
  `timeout` int DEFAULT '0' COMMENT 'time out, unit: minute',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `execution_type` tinyint DEFAULT '0' COMMENT 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`,`code`) USING BTREE,
  UNIQUE KEY `process_unique` (`name`,`project_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_process_definition_log`
--

DROP TABLE IF EXISTS `t_ds_process_definition_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_process_definition_log` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process definition name',
  `version` int DEFAULT '0' COMMENT 'process definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'description',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `release_state` tinyint DEFAULT NULL COMMENT 'process definition release state：0:offline,1:online',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process definition creator id',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'global parameters',
  `flag` tinyint DEFAULT NULL COMMENT '0 not available, 1 available',
  `locations` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'Node location information',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert group id',
  `timeout` int DEFAULT '0' COMMENT 'time out,unit: minute',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `execution_type` tinyint DEFAULT '0' COMMENT 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `operate_time` datetime DEFAULT NULL COMMENT 'operate time',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_process_instance`
--

DROP TABLE IF EXISTS `t_ds_process_instance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_process_instance` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process instance name',
  `process_definition_code` bigint NOT NULL COMMENT 'process definition code',
  `process_definition_version` int DEFAULT '0' COMMENT 'process definition version',
  `state` tinyint DEFAULT NULL COMMENT 'process instance Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete',
  `state_history` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'state history desc',
  `recovery` tinyint DEFAULT NULL COMMENT 'process instance failover flag：0:normal,1:failover instance',
  `start_time` datetime DEFAULT NULL COMMENT 'process instance start time',
  `end_time` datetime DEFAULT NULL COMMENT 'process instance end time',
  `run_times` int DEFAULT NULL COMMENT 'process instance run times',
  `host` varchar(135) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process instance host',
  `command_type` tinyint DEFAULT NULL COMMENT 'command type',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'json command parameters',
  `task_depend_type` tinyint DEFAULT NULL COMMENT 'task depend type. 0: only current node,1:before the node,2:later nodes',
  `max_try_times` tinyint DEFAULT '0' COMMENT 'max try times',
  `failure_strategy` tinyint DEFAULT '0' COMMENT 'failure strategy. 0:end the process when node failed,1:continue running the other nodes when node failed',
  `warning_type` tinyint DEFAULT '0' COMMENT 'warning type. 0:no warning,1:warning if process success,2:warning if process failed,3:warning if success',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'warning group id',
  `schedule_time` datetime DEFAULT NULL COMMENT 'schedule time',
  `command_start_time` datetime DEFAULT NULL COMMENT 'command start time',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'global parameters',
  `flag` tinyint DEFAULT '1' COMMENT 'flag',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_sub_process` int DEFAULT '0' COMMENT 'flag, whether the process is sub process',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'executor id',
  `history_cmd` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'history commands of process instance operation',
  `process_instance_priority` int DEFAULT '2' COMMENT 'process instance priority. 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker group id',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `timeout` int DEFAULT '0' COMMENT 'time out',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `var_pool` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'var_pool',
  `dry_run` tinyint DEFAULT '0' COMMENT 'dry run flag：0 normal, 1 dry run',
  `next_process_instance_id` int DEFAULT '0' COMMENT 'serial queue next processInstanceId',
  `restart_time` datetime DEFAULT NULL COMMENT 'process instance restart time',
  `test_flag` tinyint DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `process_instance_index` (`process_definition_code`,`id`) USING BTREE,
  KEY `start_time_index` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_process_task_relation`
--

DROP TABLE IF EXISTS `t_ds_process_task_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_process_task_relation` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'relation name',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `process_definition_code` bigint NOT NULL COMMENT 'process code',
  `process_definition_version` int NOT NULL COMMENT 'process version',
  `pre_task_code` bigint NOT NULL COMMENT 'pre task code',
  `pre_task_version` int NOT NULL COMMENT 'pre task version',
  `post_task_code` bigint NOT NULL COMMENT 'post task code',
  `post_task_version` int NOT NULL COMMENT 'post task version',
  `condition_type` tinyint DEFAULT NULL COMMENT 'condition type : 0 none, 1 judge 2 delay',
  `condition_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'condition params(json)',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_code` (`project_code`,`process_definition_code`) USING BTREE,
  KEY `idx_pre_task_code_version` (`pre_task_code`,`pre_task_version`) USING BTREE,
  KEY `idx_post_task_code_version` (`post_task_code`,`post_task_version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_process_task_relation_log`
--

DROP TABLE IF EXISTS `t_ds_process_task_relation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_process_task_relation_log` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'relation name',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `process_definition_code` bigint NOT NULL COMMENT 'process code',
  `process_definition_version` int NOT NULL COMMENT 'process version',
  `pre_task_code` bigint NOT NULL COMMENT 'pre task code',
  `pre_task_version` int NOT NULL COMMENT 'pre task version',
  `post_task_code` bigint NOT NULL COMMENT 'post task code',
  `post_task_version` int NOT NULL COMMENT 'post task version',
  `condition_type` tinyint DEFAULT NULL COMMENT 'condition type : 0 none, 1 judge 2 delay',
  `condition_params` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'condition params(json)',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `operate_time` datetime DEFAULT NULL COMMENT 'operate time',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_process_code_version` (`process_definition_code`,`process_definition_version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_project_bak`
--

DROP TABLE IF EXISTS `t_ds_project_bak`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_project_bak` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'project name',
  `code` bigint NOT NULL COMMENT 'encoding',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'creator id',
  `flag` tinyint DEFAULT '1' COMMENT '0 not available, 1 available',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_code` (`code`) USING BTREE,
  UNIQUE KEY `unique_name` (`name`) USING BTREE,
  KEY `user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_queue`
--

DROP TABLE IF EXISTS `t_ds_queue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_queue` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `queue_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'queue name',
  `queue` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'yarn queue name',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_queue_name` (`queue_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

INSERT INTO `t_ds_queue` VALUES ('1', 'default', 'default', NULL, NULL);
--
-- Table structure for table `t_ds_relation_datasource_user`
--

DROP TABLE IF EXISTS `t_ds_relation_datasource_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_datasource_user` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `datasource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'data source id',
  `perm` int DEFAULT '1' COMMENT 'limits of authority',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_namespace_user`
--

DROP TABLE IF EXISTS `t_ds_relation_namespace_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_namespace_user` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `namespace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'namespace id',
  `perm` int DEFAULT '1' COMMENT 'limits of authority',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `namespace_user_unique` (`user_id`,`namespace_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_process_instance`
--

DROP TABLE IF EXISTS `t_ds_relation_process_instance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_process_instance` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `parent_process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'parent process instance id',
  `parent_task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'parent process instance id',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'child process instance id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_parent_process_task` (`parent_process_instance_id`,`parent_task_instance_id`) USING BTREE,
  KEY `idx_process_instance_id` (`process_instance_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_project_user_bak`
--

DROP TABLE IF EXISTS `t_ds_relation_project_user_bak`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_project_user_bak` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `project_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'project id',
  `perm` int DEFAULT '1' COMMENT 'limits of authority',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_uid_pid` (`user_id`,`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_resources_user`
--

DROP TABLE IF EXISTS `t_ds_relation_resources_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_resources_user` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `resources_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'resource id',
  `perm` int DEFAULT '1' COMMENT 'limits of authority',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_rule_execute_sql`
--

DROP TABLE IF EXISTS `t_ds_relation_rule_execute_sql`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_rule_execute_sql` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `execute_sql_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_rule_input_entry`
--

DROP TABLE IF EXISTS `t_ds_relation_rule_input_entry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_rule_input_entry` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `rule_input_entry_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `values_map` text CHARACTER SET utf8 COLLATE utf8_general_ci,
  `index` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_relation_udfs_user`
--

DROP TABLE IF EXISTS `t_ds_relation_udfs_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_relation_udfs_user` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'userid',
  `udf_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'udf id',
  `perm` int DEFAULT '1' COMMENT 'limits of authority',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_resources`
--

DROP TABLE IF EXISTS `t_ds_resources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_resources` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alias` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alias',
  `file_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'file name',
  `description` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'user id',
  `type` tinyint DEFAULT NULL COMMENT 'resource type,0:FILE，1:UDF',
  `size` bigint DEFAULT NULL COMMENT 'resource size',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `full_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_directory` tinyint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_schedules`
--

DROP TABLE IF EXISTS `t_ds_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_schedules` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_code` bigint NOT NULL COMMENT 'process definition code',
  `start_time` datetime NOT NULL COMMENT 'start time',
  `end_time` datetime NOT NULL COMMENT 'end time',
  `timezone_id` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'schedule timezone id',
  `crontab` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'crontab description',
  `failure_strategy` tinyint NOT NULL COMMENT 'failure strategy. 0:end,1:continue',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `release_state` tinyint NOT NULL COMMENT 'release state. 0:offline,1:online ',
  `warning_type` tinyint NOT NULL COMMENT 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'alert group id',
  `process_instance_priority` int DEFAULT '2' COMMENT 'process instance priority：0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT 'worker group id',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_session`
--

DROP TABLE IF EXISTS `t_ds_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_session` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'user id',
  `ip` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'ip',
  `last_login_time` datetime DEFAULT NULL COMMENT 'last login time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_task_definition`
--

DROP TABLE IF EXISTS `t_ds_task_definition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_task_definition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` bigint NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task definition name',
  `version` int DEFAULT '0' COMMENT 'task definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'description',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task definition creator id',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int DEFAULT '0' COMMENT 'task execute type: 0-batch, 1-stream',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'job custom parameters',
  `flag` tinyint DEFAULT NULL COMMENT '0 not available, 1 available',
  `task_priority` tinyint DEFAULT '2' COMMENT 'job priority',
  `worker_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker grouping',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `fail_retry_times` int DEFAULT NULL COMMENT 'number of failed retries',
  `fail_retry_interval` int DEFAULT NULL COMMENT 'failed retry interval',
  `timeout_flag` tinyint DEFAULT '0' COMMENT 'timeout flag:0 close, 1 open',
  `timeout_notify_strategy` tinyint DEFAULT NULL COMMENT 'timeout notification policy: 0 warning, 1 fail',
  `timeout` int DEFAULT '0' COMMENT 'timeout length,unit: minute',
  `delay_time` int DEFAULT '0' COMMENT 'delay execution time,unit: minute',
  `resource_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'resource id, separated by comma',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task group id',
  `task_group_priority` tinyint DEFAULT '0' COMMENT 'task group priority',
  `cpu_quota` int NOT NULL DEFAULT '-1' COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int NOT NULL DEFAULT '-1' COMMENT 'MemoryMax(MB): -1:Infinity',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_task_definition_log`
--

DROP TABLE IF EXISTS `t_ds_task_definition_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_task_definition_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` bigint NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task definition name',
  `version` int DEFAULT '0' COMMENT 'task definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'description',
  `project_code` bigint NOT NULL COMMENT 'project code',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task definition creator id',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int DEFAULT '0' COMMENT 'task execute type: 0-batch, 1-stream',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'job custom parameters',
  `flag` tinyint DEFAULT NULL COMMENT '0 not available, 1 available',
  `task_priority` tinyint DEFAULT '2' COMMENT 'job priority',
  `worker_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker grouping',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `fail_retry_times` int DEFAULT NULL COMMENT 'number of failed retries',
  `fail_retry_interval` int DEFAULT NULL COMMENT 'failed retry interval',
  `timeout_flag` tinyint DEFAULT '0' COMMENT 'timeout flag:0 close, 1 open',
  `timeout_notify_strategy` tinyint DEFAULT NULL COMMENT 'timeout notification policy: 0 warning, 1 fail',
  `timeout` int DEFAULT '0' COMMENT 'timeout length,unit: minute',
  `delay_time` int DEFAULT '0' COMMENT 'delay execution time,unit: minute',
  `resource_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'resource id, separated by comma',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'operator user id',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task group id',
  `task_group_priority` tinyint DEFAULT '0' COMMENT 'task group priority',
  `operate_time` datetime DEFAULT NULL COMMENT 'operate time',
  `cpu_quota` int NOT NULL DEFAULT '-1' COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int NOT NULL DEFAULT '-1' COMMENT 'MemoryMax(MB): -1:Infinity',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_code_version` (`code`,`version`) USING BTREE,
  KEY `idx_project_code` (`project_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=317 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_task_group`
--

DROP TABLE IF EXISTS `t_ds_task_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_task_group` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task_group name',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `group_size` int NOT NULL COMMENT 'group size',
  `use_size` int DEFAULT '0' COMMENT 'used size',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'creator id',
  `project_code` bigint DEFAULT '0' COMMENT 'project code',
  `status` tinyint DEFAULT '1' COMMENT '0 not available, 1 available',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_task_group_queue`
--

DROP TABLE IF EXISTS `t_ds_task_group_queue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_task_group_queue` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'taskintanceid',
  `task_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'TaskInstance name',
  `group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'taskGroup id',
  `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'processInstace id',
  `priority` int DEFAULT '0' COMMENT 'priority',
  `status` tinyint DEFAULT '-1' COMMENT '-1: waiting  1: running  2: finished',
  `force_start` tinyint DEFAULT '0' COMMENT 'is force start 0 NO ,1 YES',
  `in_queue` tinyint DEFAULT '0' COMMENT 'ready to get the queue by other task finish 0 NO ,1 YES',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_task_instance`
--

DROP TABLE IF EXISTS `t_ds_task_instance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_task_instance` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task name',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int DEFAULT '0' COMMENT 'task execute type: 0-batch, 1-stream',
  `task_code` bigint NOT NULL COMMENT 'task definition code',
  `task_definition_version` int DEFAULT '0' COMMENT 'task definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'process instance id',
  `state` tinyint DEFAULT NULL COMMENT 'Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete',
  `submit_time` datetime DEFAULT NULL COMMENT 'task submit time',
  `start_time` datetime DEFAULT NULL COMMENT 'task start time',
  `end_time` datetime DEFAULT NULL COMMENT 'task end time',
  `host` varchar(135) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'host of task running on',
  `execute_path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task execute path in the host',
  `log_path` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'task log path',
  `alert_flag` tinyint DEFAULT NULL COMMENT 'whether alert',
  `retry_times` int DEFAULT '0' COMMENT 'task retry times',
  `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'pid of task',
  `app_link` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'yarn app id',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'job custom parameters',
  `flag` tinyint DEFAULT '1' COMMENT '0 not available, 1 available',
  `retry_interval` int DEFAULT NULL COMMENT 'retry interval when task failed ',
  `max_retry_times` int DEFAULT NULL COMMENT 'max retry times',
  `task_instance_priority` int DEFAULT NULL COMMENT 'task instance priority:0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'worker group id',
  `environment_code` bigint DEFAULT '-1' COMMENT 'environment code',
  `environment_config` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'this config contains many environment variables config',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `first_submit_time` datetime DEFAULT NULL COMMENT 'task first submit time',
  `delay_time` int DEFAULT '0' COMMENT 'task delay execution time',
  `var_pool` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'var_pool',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'task group id',
  `dry_run` tinyint DEFAULT '0' COMMENT 'dry run flag: 0 normal, 1 dry run',
  `cpu_quota` int NOT NULL DEFAULT '-1' COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int NOT NULL DEFAULT '-1' COMMENT 'MemoryMax(MB): -1:Infinity',
  `test_flag` tinyint DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `process_instance_id` (`process_instance_id`) USING BTREE,
  KEY `idx_code_version` (`task_code`,`task_definition_version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_tenant_bak`
--

DROP TABLE IF EXISTS `t_ds_tenant_bak`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_tenant_bak` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tenant_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'tenant code',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `queue_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'queue id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_tenant_code` (`tenant_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_udfs`
--

DROP TABLE IF EXISTS `t_ds_udfs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_udfs` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `func_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'UDF function name',
  `class_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'class of udf',
  `type` tinyint NOT NULL COMMENT 'Udf function type',
  `arg_types` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'arguments types',
  `database` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'data base',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `resource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'resource id',
  `resource_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'resource name',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_func_name` (`func_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_user_bak`
--

DROP TABLE IF EXISTS `t_ds_user_bak`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_user_bak` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'user name',
  `user_password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'user password',
  `user_type` tinyint DEFAULT NULL COMMENT 'user type, 0:administrator，1:ordinary user',
  `email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'email',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'phone',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'tenant id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `queue` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'queue',
  `state` tinyint DEFAULT '1' COMMENT 'state 0:disable 1:enable',
  `time_zone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'time zone',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `user_name_unique` (`user_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_version`
--

DROP TABLE IF EXISTS `t_ds_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_version` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `version` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `version_UNIQUE` (`version`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='version';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ds_worker_group`
--

DROP TABLE IF EXISTS `t_ds_worker_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ds_worker_group` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'worker group name',
  `addr_list` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'worker addr list. split by [,]',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'description',
  `other_params_json` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT 'other params json',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name_unique` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-11-24 10:45:39
