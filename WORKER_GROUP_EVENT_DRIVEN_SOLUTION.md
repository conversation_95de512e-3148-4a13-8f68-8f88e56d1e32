# Worker Group 事件驱动自动管理解决方案

## 🎯 功能概述

基于海豚调度器现有的事件驱动机制，实现 `t_ds_worker_group` 表的自动管理：
- **事件驱动**：利用现有的 `SubscribeDataManager` 监听 worker 注册数据变化
- **实时响应**：worker 上线/下线时立即触发事件处理
- **无额外开销**：不需要定时任务，基于现有心跳机制

## 🏗️ 架构设计

### 核心组件

1. **WorkerGroupAutoManager** - 事件监听器
   - 实现 `SubscribeListener` 接口
   - 监听 `/dolphinscheduler/workers` 路径下的数据变化
   - 处理 ADD/UPDATE/REMOVE 事件

2. **事件驱动机制**
   - 利用现有的 `SubscribeDataManager`
   - 当 `t_ds_jdbc_registry_data` 表中 worker 数据变化时自动触发
   - 无需额外的定时任务

## 📊 工作流程

### 事件触发机制
```
Worker 心跳 → t_ds_jdbc_registry_data 表变化 → SubscribeDataManager 检测 → 触发 WorkerGroupAutoManager.notify()
```

### Worker 上线流程
```
1. Worker 启动并发送心跳
2. t_ds_jdbc_registry_data 表新增记录
3. SubscribeDataManager 检测到 ADD 事件
4. WorkerGroupAutoManager.notify() 被调用
5. 解析 worker 信息并更新 t_ds_worker_group 表
```

### Worker 下线流程
```
1. Worker 停止，心跳超时
2. t_ds_jdbc_registry_data 表删除记录
3. SubscribeDataManager 检测到 REMOVE 事件
4. WorkerGroupAutoManager.notify() 被调用
5. 从 t_ds_worker_group 表中移除或更新记录
```

## 🔧 技术实现

### 关键代码结构

#### 1. WorkerGroupAutoManager 实现 SubscribeListener
```java
@Slf4j
public class WorkerGroupAutoManager implements SubscribeListener, AutoCloseable {
    
    @Override
    public void notify(Event event) {
        String path = event.path();
        Event.Type type = event.type();
        
        if (!isWorkerPath(path)) {
            return;
        }
        
        switch (type) {
            case ADD:
            case UPDATE:
                handleWorkerOnline(path, event.data());
                break;
            case REMOVE:
                handleWorkerOffline(path);
                break;
        }
    }
}
```

#### 2. 事件处理方法
```java
private void handleWorkerOnline(String path, String heartBeatData) {
    String workerGroupName = extractWorkerGroupFromPath(path);
    String workerAddress = extractWorkerAddressFromPath(path);
    
    // 验证心跳数据
    WorkerHeartBeat heartBeat = JSONUtils.parseObject(heartBeatData, WorkerHeartBeat.class);
    
    // 更新缓存和数据库
    currentWorkerGroups.computeIfAbsent(workerGroupName, k -> new HashSet<>()).add(workerAddress);
    updateWorkerGroupTable(workerGroupName, currentWorkerGroups.get(workerGroupName));
}

private void handleWorkerOffline(String path) {
    String workerGroupName = extractWorkerGroupFromPath(path);
    String workerAddress = extractWorkerAddressFromPath(path);
    
    // 从缓存中移除并更新数据库
    Set<String> addresses = currentWorkerGroups.get(workerGroupName);
    if (addresses != null) {
        addresses.remove(workerAddress);
        if (addresses.isEmpty()) {
            deleteWorkerGroupTable(workerGroupName);
            currentWorkerGroups.remove(workerGroupName);
        } else {
            updateWorkerGroupTable(workerGroupName, addresses);
        }
    }
}
```

#### 3. 监听器注册
```java
// 在 JdbcRegistry.start() 中注册监听器
workerGroupAutoManager.start();
subscribeDataManager.addListener("/dolphinscheduler/workers", workerGroupAutoManager);
```

## 📋 与现有机制的集成

### 利用现有组件

1. **SubscribeDataManager**
   - 已有的数据变化监听机制
   - 定期检查 `t_ds_jdbc_registry_data` 表变化
   - 触发相应的事件通知

2. **EphemeralDateManager**
   - 管理临时数据（心跳数据）
   - 自动清理过期数据
   - 当 worker 停止时自动删除相关记录

3. **JdbcOperator**
   - 扩展了 worker group 相关的数据库操作
   - 复用现有的数据库连接和事务管理

### 事件流程图
```
Worker 心跳更新
    ↓
t_ds_jdbc_registry_data 表变化
    ↓
SubscribeDataManager.RegistrySubscribeDataCheckTask
    ↓
检测数据变化 (ADD/UPDATE/REMOVE)
    ↓
触发 SubscribeListener.notify()
    ↓
WorkerGroupAutoManager.notify()
    ↓
更新 t_ds_worker_group 表
```

## 🚀 优势特点

### 1. 事件驱动，实时响应
- 基于现有的数据变化监听机制
- worker 状态变化时立即触发处理
- 响应时间取决于 `term-refresh-interval` 配置（默认2秒）

### 2. 零额外开销
- 不需要额外的定时任务
- 复用现有的心跳和监听机制
- 不增加系统负载

### 3. 可靠性高
- 基于数据库事务保证一致性
- 利用现有的错误处理和重试机制
- 自动处理网络断开和重连场景

### 4. 易于维护
- 代码结构清晰，职责单一
- 与现有架构完美集成
- 详细的日志记录便于调试

## 📊 配置和部署

### 1. 配置要求
```yaml
registry:
  type: jdbc
  term-refresh-interval: 2s  # 事件检测间隔
  term-expire-times: 3       # 心跳超时倍数
```

### 2. 关键日志
```
[INFO] Starting WorkerGroupAutoManager...
[INFO] WorkerGroupAutoManager started, loaded 2 worker groups
[INFO] 检测到 worker 上线: 192.168.1.100:1234 -> default
[INFO] 创建 worker group: default -> 192.168.1.100:1234
[INFO] 检测到 worker 下线: 192.168.1.100:1234 -> default
[INFO] 删除 worker group: default
```

### 3. 监控指标
- 事件处理成功率
- 数据库操作耗时
- Worker group 记录数量变化
- 事件处理延迟

## 🔍 故障排查

### 常见问题

1. **事件未触发**
   - 检查 `SubscribeDataManager` 是否正常运行
   - 确认监听器是否正确注册
   - 查看 `term-refresh-interval` 配置

2. **数据不一致**
   - 检查数据库事务是否正常
   - 确认心跳数据格式是否正确
   - 查看错误日志中的异常信息

3. **性能问题**
   - 监控事件处理频率
   - 检查数据库连接池状态
   - 优化 SQL 查询性能

### 调试方法
```bash
# 查看事件监听日志
tail -f dolphinscheduler-master.log | grep "WorkerGroupAutoManager"

# 查看注册数据变化
SELECT * FROM t_ds_jdbc_registry_data WHERE data_key LIKE '/dolphinscheduler/workers/%' ORDER BY last_term DESC;

# 查看 worker group 记录
SELECT * FROM t_ds_worker_group ORDER BY update_time DESC;
```

## 💡 扩展功能

### 可能的增强
1. **批量处理**：支持批量处理多个 worker 变化事件
2. **事件过滤**：只处理特定 worker group 的变化
3. **状态验证**：定期验证缓存与数据库的一致性
4. **指标统计**：记录 worker 上线/下线的统计信息

## 🎉 总结

这个基于事件驱动的解决方案具有以下优势：

1. **高效**：利用现有机制，无额外开销
2. **实时**：基于事件驱动，响应迅速
3. **可靠**：基于数据库事务，保证一致性
4. **简洁**：代码结构清晰，易于维护
5. **集成**：与现有架构完美融合

通过这个方案，可以实现 worker group 的完全自动化管理，大大简化运维工作，提高系统的自动化程度。
