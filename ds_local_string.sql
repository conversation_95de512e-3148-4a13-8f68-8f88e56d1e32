/*
 Navicat Premium Data Transfer

 Source Server         : **************_open
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : **************:13306
 Source Schema         : ds_local_string

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 14/11/2023 13:59:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for QRTZ_BLOB_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_BLOB_TRIGGERS`;
CREATE TABLE `QRTZ_BLOB_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `BLOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `SCHED_NAME`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_BLOB_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_BLOB_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_CALENDARS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_CALENDARS`;
CREATE TABLE `QRTZ_CALENDARS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CALENDAR_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CALENDAR` blob NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_CALENDARS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_CRON_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_CRON_TRIGGERS`;
CREATE TABLE `QRTZ_CRON_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CRON_EXPRESSION` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TIME_ZONE_ID` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_CRON_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_CRON_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_FIRED_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_FIRED_TRIGGERS`;
CREATE TABLE `QRTZ_FIRED_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ENTRY_ID` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `FIRED_TIME` bigint(0) NOT NULL,
  `SCHED_TIME` bigint(0) NOT NULL,
  `PRIORITY` int(0) NOT NULL,
  `STATE` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `ENTRY_ID`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TRIG_INST_NAME`(`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_FT_INST_JOB_REQ_RCVRY`(`SCHED_NAME`, `INSTANCE_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_FT_J_G`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_T_G`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_FT_TG`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_FIRED_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_JOB_DETAILS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_JOB_DETAILS`;
CREATE TABLE `QRTZ_JOB_DETAILS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `JOB_CLASS_NAME` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_DURABLE` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_NONCONCURRENT` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `IS_UPDATE_DATA` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `REQUESTS_RECOVERY` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_J_REQ_RECOVERY`(`SCHED_NAME`, `REQUESTS_RECOVERY`) USING BTREE,
  INDEX `IDX_QRTZ_J_GRP`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_JOB_DETAILS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_LOCKS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_LOCKS`;
CREATE TABLE `QRTZ_LOCKS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `LOCK_NAME` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `LOCK_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_LOCKS
-- ----------------------------
INSERT INTO `QRTZ_LOCKS` VALUES ('DolphinScheduler', 'STATE_ACCESS');
INSERT INTO `QRTZ_LOCKS` VALUES ('DolphinScheduler', 'TRIGGER_ACCESS');

-- ----------------------------
-- Table structure for QRTZ_PAUSED_TRIGGER_GRPS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_PAUSED_TRIGGER_GRPS`;
CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_PAUSED_TRIGGER_GRPS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_SCHEDULER_STATE
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_SCHEDULER_STATE`;
CREATE TABLE `QRTZ_SCHEDULER_STATE`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `INSTANCE_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `LAST_CHECKIN_TIME` bigint(0) NOT NULL,
  `CHECKIN_INTERVAL` bigint(0) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `INSTANCE_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_SCHEDULER_STATE
-- ----------------------------
INSERT INTO `QRTZ_SCHEDULER_STATE` VALUES ('DolphinScheduler', 'XiaoXinAir1699581583497', 1699595143715, 5000);

-- ----------------------------
-- Table structure for QRTZ_SIMPLE_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_SIMPLE_TRIGGERS`;
CREATE TABLE `QRTZ_SIMPLE_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `REPEAT_COUNT` bigint(0) NOT NULL,
  `REPEAT_INTERVAL` bigint(0) NOT NULL,
  `TIMES_TRIGGERED` bigint(0) NOT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_SIMPLE_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_SIMPROP_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_SIMPROP_TRIGGERS`;
CREATE TABLE `QRTZ_SIMPROP_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `STR_PROP_1` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `STR_PROP_2` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `STR_PROP_3` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `INT_PROP_1` int(0) NULL DEFAULT NULL,
  `INT_PROP_2` int(0) NULL DEFAULT NULL,
  `LONG_PROP_1` bigint(0) NULL DEFAULT NULL,
  `LONG_PROP_2` bigint(0) NULL DEFAULT NULL,
  `DEC_PROP_1` decimal(13, 4) NULL DEFAULT NULL,
  `DEC_PROP_2` decimal(13, 4) NULL DEFAULT NULL,
  `BOOL_PROP_1` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BOOL_PROP_2` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) REFERENCES `QRTZ_TRIGGERS` (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_SIMPROP_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for QRTZ_TRIGGERS
-- ----------------------------
DROP TABLE IF EXISTS `QRTZ_TRIGGERS`;
CREATE TABLE `QRTZ_TRIGGERS`  (
  `SCHED_NAME` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOB_GROUP` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `DESCRIPTION` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NEXT_FIRE_TIME` bigint(0) NULL DEFAULT NULL,
  `PREV_FIRE_TIME` bigint(0) NULL DEFAULT NULL,
  `PRIORITY` int(0) NULL DEFAULT NULL,
  `TRIGGER_STATE` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TRIGGER_TYPE` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `START_TIME` bigint(0) NOT NULL,
  `END_TIME` bigint(0) NULL DEFAULT NULL,
  `CALENDAR_NAME` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MISFIRE_INSTR` smallint(0) NULL DEFAULT NULL,
  `JOB_DATA` blob NULL,
  PRIMARY KEY (`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_J`(`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_JG`(`SCHED_NAME`, `JOB_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_C`(`SCHED_NAME`, `CALENDAR_NAME`) USING BTREE,
  INDEX `IDX_QRTZ_T_G`(`SCHED_NAME`, `TRIGGER_GROUP`) USING BTREE,
  INDEX `IDX_QRTZ_T_STATE`(`SCHED_NAME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_STATE`(`SCHED_NAME`, `TRIGGER_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_N_G_STATE`(`SCHED_NAME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NEXT_FIRE_TIME`(`SCHED_NAME`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST`(`SCHED_NAME`, `TRIGGER_STATE`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_STATE`) USING BTREE,
  INDEX `IDX_QRTZ_T_NFT_ST_MISFIRE_GRP`(`SCHED_NAME`, `MISFIRE_INSTR`, `NEXT_FIRE_TIME`, `TRIGGER_GROUP`, `TRIGGER_STATE`) USING BTREE,
  CONSTRAINT `QRTZ_TRIGGERS_ibfk_1` FOREIGN KEY (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) REFERENCES `QRTZ_JOB_DETAILS` (`SCHED_NAME`, `JOB_NAME`, `JOB_GROUP`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of QRTZ_TRIGGERS
-- ----------------------------

-- ----------------------------
-- Table structure for shedlock
-- ----------------------------
DROP TABLE IF EXISTS `shedlock`;
CREATE TABLE `shedlock`  (
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '锁名称',
  `lock_until` datetime(0) NULL DEFAULT NULL COMMENT '释放锁时间',
  `locked_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '获取锁时间',
  `locked_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '主机名称',
  PRIMARY KEY (`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin COMMENT = '定时任务锁' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shedlock
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_access_token
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_access_token`;
CREATE TABLE `t_ds_access_token`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'user id',
  `token` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'token',
  `expire_time` datetime(0) NULL DEFAULT NULL COMMENT 'end time of token ',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_access_token
-- ----------------------------
INSERT INTO `t_ds_access_token` VALUES ('1', '4', 'ee940593-3470-4413-916a-bb12c9a8b302', '2023-09-06 16:39:23', '2023-09-05 16:39:28', '2023-09-05 16:39:30');

-- ----------------------------
-- Table structure for t_ds_alert
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_alert`;
CREATE TABLE `t_ds_alert`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'title',
  `sign` char(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'sign=sha1(content)',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'Message content (can be email, can be SMS. Mail is stored in JSON map, and SMS is string)',
  `alert_status` tinyint(0) NULL DEFAULT 0 COMMENT '0:wait running,1:success,2:failed',
  `warning_type` tinyint(0) NULL DEFAULT 2 COMMENT '1 process is successfully, 2 process/task is failed',
  `log` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'log',
  `alertgroup_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert group id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `project_code` bigint(0) NULL DEFAULT NULL COMMENT 'project_code',
  `process_definition_code` bigint(0) NULL DEFAULT NULL COMMENT 'process_definition_code',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process_instance_id',
  `alert_type` int(0) NULL DEFAULT NULL COMMENT 'alert_type',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`alert_status`) USING BTREE,
  INDEX `idx_sign`(`sign`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8644778034655 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_alert
-- ----------------------------
INSERT INTO `t_ds_alert` VALUES (8644778034633, 'Fault tolerance warning', '41a2286d8b07b9429a905d220046b0e29df66eac', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/************:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-05 15:59:18', '2023-10-10 16:07:18', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034634, 'Fault tolerance warning', 'cef034192bfe4c2867942b3ec75212a66533ebeb', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-06 16:56:26', '2023-10-10 16:07:18', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034635, 'Fault tolerance warning', '052499ff165332653e60211c2307fe6a736192db', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/***********:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-07 17:38:28', '2023-10-10 16:07:19', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034636, 'Fault tolerance warning', '4144ffbdb0e219c6677f2a18995310c62e50612a', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-08 14:08:58', '2023-10-10 16:07:19', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034637, 'Fault tolerance warning', '60e9d39ef75c42a9271277ae61256c2abb8202f7', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-09 12:01:30', '2023-10-10 16:07:21', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034638, 'Fault tolerance warning', 'b6e9aec17a8d43b59471515310ae520251a9607a', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/************:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-11 10:47:42', '2023-10-10 16:07:22', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034639, 'Fault tolerance warning', 'b6e9aec17a8d43b59471515310ae520251a9607a', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/************:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-11 12:48:45', '2023-10-10 16:07:23', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034640, 'Fault tolerance warning', 'b6e9aec17a8d43b59471515310ae520251a9607a', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/************:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-11 13:53:16', '2023-10-10 16:07:23', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034641, 'Fault tolerance warning', '15ddc5d2a13587a4608889a6a4d14415eea2d101', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/************:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-09-20 16:52:19', '2023-10-10 16:07:23', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034642, 'Fault tolerance warning', '1dc010306f74ee5b6f9ea23066ba4238dfe89ad6', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 10:26:48', '2023-10-10 16:07:24', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034643, 'Fault tolerance warning', '1dc010306f74ee5b6f9ea23066ba4238dfe89ad6', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 11:47:05', '2023-10-10 16:07:24', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034644, 'Fault tolerance warning', '1dc010306f74ee5b6f9ea23066ba4238dfe89ad6', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 13:46:10', '2023-10-10 16:07:25', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034645, 'Fault tolerance warning', '1dc010306f74ee5b6f9ea23066ba4238dfe89ad6', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 14:53:08', '2023-10-10 16:07:25', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034646, 'Fault tolerance warning', '1dc010306f74ee5b6f9ea23066ba4238dfe89ad6', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 16:16:42', '2023-10-10 16:16:46', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034647, 'Fault tolerance warning', 'e2aa46cfbd13c42a921c6cc898bdc7faf2c9f4a6', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/**************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-10 16:25:44', '2023-10-10 16:25:49', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034648, 'Fault tolerance warning', '8e12d3825ced98bdea6057281ef6fddabcfae000', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-26 14:24:37', '2023-10-26 14:24:39', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034649, 'Fault tolerance warning', '8df42d95fe6ae87c12940361344bad68c6671961', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-30 17:51:28', '2023-10-30 17:51:29', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034650, 'Fault tolerance warning', '8df42d95fe6ae87c12940361344bad68c6671961', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-30 17:53:36', '2023-10-30 17:53:40', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034651, 'Fault tolerance warning', '8df42d95fe6ae87c12940361344bad68c6671961', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-30 18:03:54', '2023-10-30 18:03:55', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034652, 'Fault tolerance warning', '8df42d95fe6ae87c12940361344bad68c6671961', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/************:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-10-30 18:17:24', '2023-10-30 18:17:26', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034653, 'Fault tolerance warning', '580fe935db35629f66b01e3a5d5281b194f703ff', '[{\"type\":\"MASTER\",\"host\":\"/nodes/master/***********:5678\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 2, 2, '[{\"status\":\"false\",\"message\":\"no bind plugin instance\"}]', '1', '2023-11-07 16:08:32', '2023-11-07 16:08:35', NULL, NULL, NULL, 4);
INSERT INTO `t_ds_alert` VALUES (8644778034654, 'Fault tolerance warning', '563923abf2f8ee51eac15c34c632a33e17620ee8', '[{\"type\":\"WORKER\",\"host\":\"/nodes/worker/default/***********:1234\",\"event\":\"SERVER_DOWN\",\"warningLevel\":\"SERIOUS\"}]', 0, 2, NULL, '1', '2023-11-07 17:10:19', '2023-11-07 17:10:19', NULL, NULL, NULL, 4);

-- ----------------------------
-- Table structure for t_ds_alert_plugin_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_alert_plugin_instance`;
CREATE TABLE `t_ds_alert_plugin_instance`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_define_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_instance_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'plugin instance params. Also contain the params value which user input in web ui.',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `instance_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert instance name',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_alert_plugin_instance
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_alert_send_status
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_alert_send_status`;
CREATE TABLE `t_ds_alert_send_status`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_plugin_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `send_status` tinyint(0) NULL DEFAULT 0,
  `log` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `alert_send_status_unique`(`alert_id`, `alert_plugin_instance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_alert_send_status
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_alertgroup
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_alertgroup`;
CREATE TABLE `t_ds_alertgroup`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alert_instance_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert instance ids',
  `create_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'create user id',
  `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'group name',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `t_ds_alertgroup_name_un`(`group_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_alertgroup
-- ----------------------------
INSERT INTO `t_ds_alertgroup` VALUES ('1', '7857674080320', '1', 'default admin warning group', 'default admin warning group', '2022-10-13 10:18:04', '2023-02-18 18:26:21');

-- ----------------------------
-- Table structure for t_ds_audit_log
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_audit_log`;
CREATE TABLE `t_ds_audit_log`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `resource_type` int(0) NOT NULL COMMENT 'resource type',
  `operation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'operation',
  `time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT 'create time',
  `resource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'resource id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_audit_log
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_catalog
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_catalog`;
CREATE TABLE `t_ds_catalog`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目录名',
  `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级id',
  `level` int(0) NULL DEFAULT NULL COMMENT '层级',
  `parent_names` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级名称 以中横线分割（含自己）',
  `parent_ids` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级id 以中横线分割 （含自己）',
  `project_code` bigint(0) NULL DEFAULT NULL COMMENT 'project Id',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'creator id',
  `description` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_catalog
-- ----------------------------
INSERT INTO `t_ds_catalog` VALUES ('11166257359136', '一级目录', '-1', 0, '一级目录', '11166257359136', **************, '3', '4', '描述', '2023-10-07 16:19:46', '2023-10-07 16:19:46');
INSERT INTO `t_ds_catalog` VALUES ('11166260551072', '二级目录', '11166257359136', 1, '一级目录,二级目录', '11166257359136,11166260551072', **************, '3', '4', '描述', '2023-10-07 16:20:11', '2023-10-07 16:20:11');
INSERT INTO `t_ds_catalog` VALUES ('11166260551073', '三级目录', '11166260551072', 2, '一级目录,二级目录,三级目录', '11166257359136,11166260551072,11166260551073', **************, '3', '4', '描述', '2023-10-07 16:20:11', '2023-10-07 16:20:11');
INSERT INTO `t_ds_catalog` VALUES ('11166260551074', '一级级目录', '-1', 0, '一级级目录', '11166260551074', **************, '3', '4', '描述', '2023-10-07 16:20:11', '2023-10-07 16:20:11');

-- ----------------------------
-- Table structure for t_ds_catalog_process_definition_relation
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_catalog_process_definition_relation`;
CREATE TABLE `t_ds_catalog_process_definition_relation`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `catalog_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目录id',
  `process_definition_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作流code',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_catalog_process_definition_relation
-- ----------------------------
INSERT INTO `t_ds_catalog_process_definition_relation` VALUES ('11178114471457', '11166257359136', '11178114471456', '2023-10-08 18:03:39', '2023-10-08 18:03:39');

-- ----------------------------
-- Table structure for t_ds_cluster
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_cluster`;
CREATE TABLE `t_ds_cluster`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint(0) NULL DEFAULT NULL COMMENT 'encoding',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'cluster name',
  `config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'this config contains many cluster variables config',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'the details',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `cluster_name_unique`(`name`) USING BTREE,
  UNIQUE INDEX `cluster_code_unique`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_cluster
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_command
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_command`;
CREATE TABLE `t_ds_command`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `command_type` tinyint(0) NULL DEFAULT NULL COMMENT 'Command type: 0 start workflow, 1 start execution from current node, 2 resume fault-tolerant workflow, 3 resume pause process, 4 start execution from failed node, 5 complement, 6 schedule, 7 rerun, 8 pause, 9 stop, 10 resume waiting thread',
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process definition code',
  `process_definition_version` int(0) NULL DEFAULT 0 COMMENT 'process definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT 'process instance id',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'json command parameters',
  `task_depend_type` tinyint(0) NULL DEFAULT NULL COMMENT 'Node dependency type: 0 current node, 1 forward, 2 backward',
  `failure_strategy` tinyint(0) NULL DEFAULT 0 COMMENT 'Failed policy: 0 end, 1 continue',
  `warning_type` tinyint(0) NULL DEFAULT 0 COMMENT 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'warning group',
  `schedule_time` datetime(0) NULL DEFAULT NULL COMMENT 'schedule time',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT 'start time',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'executor id',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `process_instance_priority` int(0) NULL DEFAULT 2 COMMENT 'process instance priority: 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker group',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `dry_run` tinyint(0) NULL DEFAULT 0 COMMENT 'dry run flag：0 normal, 1 dry run',
  `test_flag` tinyint(0) NULL DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `tenant_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `priority_id_index`(`process_instance_priority`, `id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_command
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_datasource
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_datasource`;
CREATE TABLE `t_ds_datasource`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'data source name',
  `note` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'description',
  `type` tinyint(0) NOT NULL COMMENT 'data source type: 0:mysql,1:postgresql,2:hive,3:spark',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the creator id',
  `connection_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'json connection params',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `t_ds_datasource_name_un`(`name`, `type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_datasource
-- ----------------------------
INSERT INTO `t_ds_datasource` VALUES ('7712840726720', '104mysql', '', 0, '1', '{\"user\":\"root\",\"password\":\"joyadata\",\"address\":\"jdbc:mysql://**************:3306\",\"database\":\"cindasc\",\"jdbcUrl\":\"jdbc:mysql://**************:3306/cindasc\",\"driverClassName\":\"com.mysql.cj.jdbc.Driver\",\"validationQuery\":\"select 1\"}', '2022-11-29 09:56:08', '2022-11-29 09:56:08');
INSERT INTO `t_ds_datasource` VALUES ('7713892600896', '104mysql1', '', 0, '1', '{\"user\":\"root\",\"password\":\"joyadata\",\"address\":\"jdbc:mysql://**************:3306\",\"database\":\"cindasc\",\"jdbcUrl\":\"jdbc:mysql://**************:3306/cindasc\",\"driverClassName\":\"com.mysql.cj.jdbc.Driver\",\"validationQuery\":\"select 1\"}', '2022-11-29 12:13:06', '2022-11-29 12:13:06');
INSERT INTO `t_ds_datasource` VALUES ('7714035633216', 'oracle', '', 5, '1', '{\"user\":\"scott\",\"password\":\"joyadata\",\"address\":\"jdbc:oracle:thin:@//**************:1521\",\"database\":\"helowin\",\"jdbcUrl\":\"jdbc:oracle:thin:@//**************:1521/helowin\",\"driverClassName\":\"oracle.jdbc.OracleDriver\",\"validationQuery\":\"select 1 from dual\",\"other\":\"k1=v1\",\"props\":{\"k1\":\"v1\"}}', '2022-11-29 12:31:43', '2022-11-30 11:52:47');
INSERT INTO `t_ds_datasource` VALUES ('7881438464320', 'lihj104mysql', '', 0, '4', '{\"user\":\"root\",\"password\":\"joyadata\",\"address\":\"jdbc:mysql://**************:3306\",\"database\":\"test\",\"jdbcUrl\":\"jdbc:mysql://**************:3306/test\",\"driverClassName\":\"com.mysql.cj.jdbc.Driver\",\"validationQuery\":\"select 1\"}', '2022-12-14 15:48:58', '2022-12-14 15:48:58');
INSERT INTO `t_ds_datasource` VALUES ('8014685516224', 'default', '', 2, '4', '{\"user\":\"hive\",\"password\":\"hive\",\"address\":\"*********************************\",\"database\":\"default\",\"jdbcUrl\":\"*********************************/default\",\"driverClassName\":\"org.apache.hive.jdbc.HiveDriver\",\"validationQuery\":\"select 1\"}', '2022-12-26 16:58:51', '2022-12-26 16:58:51');
INSERT INTO `t_ds_datasource` VALUES ('8022822214208', '**************', '', 2, '4', '{\"user\":\"hive\",\"password\":\"\",\"address\":\"jdbc:hive2://**************:10000\",\"database\":\"default\",\"jdbcUrl\":\"*****************************************\",\"driverClassName\":\"org.apache.hive.jdbc.HiveDriver\",\"validationQuery\":\"select 1\"}', '2022-12-27 10:38:19', '2022-12-27 10:38:19');

-- ----------------------------
-- Table structure for t_ds_dq_comparison_type
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_comparison_type`;
CREATE TABLE `t_ds_dq_comparison_type`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `execute_sql` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `output_table` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_inner_source` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_comparison_type
-- ----------------------------
INSERT INTO `t_ds_dq_comparison_type` VALUES ('1', 'FixValue', NULL, NULL, NULL, '2022-10-13 10:18:07', '2022-10-13 10:18:07', 0);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('2', 'DailyAvg', 'select round(avg(statistics_value),2) as day_avg from t_ds_dq_task_statistics_value where data_time >=date_trunc(\'DAY\', ${data_time}) and data_time < date_add(date_trunc(\'day\', ${data_time}),1) and unique_code = ${unique_code} and statistics_name = \'${statistics_name}\'', 'day_range', 'day_range.day_avg', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 1);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('3', 'WeeklyAvg', 'select round(avg(statistics_value),2) as week_avg from t_ds_dq_task_statistics_value where  data_time >= date_trunc(\'WEEK\', ${data_time}) and data_time <date_trunc(\'day\', ${data_time}) and unique_code = ${unique_code} and statistics_name = \'${statistics_name}\'', 'week_range', 'week_range.week_avg', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 1);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('4', 'MonthlyAvg', 'select round(avg(statistics_value),2) as month_avg from t_ds_dq_task_statistics_value where  data_time >= date_trunc(\'MONTH\', ${data_time}) and data_time <date_trunc(\'day\', ${data_time}) and unique_code = ${unique_code} and statistics_name = \'${statistics_name}\'', 'month_range', 'month_range.month_avg', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 1);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('5', 'Last7DayAvg', 'select round(avg(statistics_value),2) as last_7_avg from t_ds_dq_task_statistics_value where  data_time >= date_add(date_trunc(\'day\', ${data_time}),-7) and  data_time <date_trunc(\'day\', ${data_time}) and unique_code = ${unique_code} and statistics_name = \'${statistics_name}\'', 'last_seven_days', 'last_seven_days.last_7_avg', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 1);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('6', 'Last30DayAvg', 'select round(avg(statistics_value),2) as last_30_avg from t_ds_dq_task_statistics_value where  data_time >= date_add(date_trunc(\'day\', ${data_time}),-30) and  data_time < date_trunc(\'day\', ${data_time}) and unique_code = ${unique_code} and statistics_name = \'${statistics_name}\'', 'last_thirty_days', 'last_thirty_days.last_30_avg', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 1);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('7', 'SrcTableTotalRows', 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'total_count', 'total_count.total', '2022-10-13 10:18:07', '2022-10-13 10:18:07', 0);
INSERT INTO `t_ds_dq_comparison_type` VALUES ('8', 'TargetTableTotalRows', 'SELECT COUNT(*) AS total FROM ${target_table} WHERE (${target_filter})', 'total_count', 'total_count.total', '2022-10-13 10:18:08', '2022-10-13 10:18:08', 0);

-- ----------------------------
-- Table structure for t_ds_dq_execute_result
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_execute_result`;
CREATE TABLE `t_ds_dq_execute_result`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `rule_type` int(0) NULL DEFAULT NULL,
  `rule_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `statistics_value` double NULL DEFAULT NULL,
  `comparison_value` double NULL DEFAULT NULL,
  `check_type` int(0) NULL DEFAULT NULL,
  `threshold` double NULL DEFAULT NULL,
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `failure_strategy` int(0) NULL DEFAULT NULL,
  `state` int(0) NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `comparison_type` int(0) NULL DEFAULT NULL,
  `error_output_path` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_execute_result
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_dq_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_rule`;
CREATE TABLE `t_ds_dq_rule`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` int(0) NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_rule
-- ----------------------------
INSERT INTO `t_ds_dq_rule` VALUES ('1', '$t(null_check)', 0, '1', '2022-10-13 10:18:09', '2022-10-13 10:18:09');
INSERT INTO `t_ds_dq_rule` VALUES ('10', '$t(table_count_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');
INSERT INTO `t_ds_dq_rule` VALUES ('2', '$t(custom_sql)', 1, '1', '2022-10-13 10:18:09', '2022-10-13 10:18:09');
INSERT INTO `t_ds_dq_rule` VALUES ('3', '$t(multi_table_accuracy)', 2, '1', '2022-10-13 10:18:09', '2022-10-13 10:18:09');
INSERT INTO `t_ds_dq_rule` VALUES ('4', '$t(multi_table_value_comparison)', 3, '1', '2022-10-13 10:18:09', '2022-10-13 10:18:09');
INSERT INTO `t_ds_dq_rule` VALUES ('5', '$t(field_length_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');
INSERT INTO `t_ds_dq_rule` VALUES ('6', '$t(uniqueness_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');
INSERT INTO `t_ds_dq_rule` VALUES ('7', '$t(regexp_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');
INSERT INTO `t_ds_dq_rule` VALUES ('8', '$t(timeliness_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');
INSERT INTO `t_ds_dq_rule` VALUES ('9', '$t(enumeration_check)', 0, '1', '2022-10-13 10:18:10', '2022-10-13 10:18:10');

-- ----------------------------
-- Table structure for t_ds_dq_rule_execute_sql
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_rule_execute_sql`;
CREATE TABLE `t_ds_dq_rule_execute_sql`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `index` int(0) NULL DEFAULT NULL,
  `sql` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `table_alias` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` int(0) NULL DEFAULT NULL,
  `is_error_output_sql` tinyint(1) NULL DEFAULT 0,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_rule_execute_sql
-- ----------------------------
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('1', 1, 'SELECT COUNT(*) AS nulls FROM null_items', 'null_count', 1, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('10', 1, 'SELECT COUNT(*) AS regexps FROM regexp_items', 'regexp_count', 1, 0, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('11', 1, 'SELECT * FROM ${src_table} WHERE (to_unix_timestamp(${src_field}, \'${datetime_format}\')-to_unix_timestamp(\'${deadline}\', \'${datetime_format}\') <= 0) AND (to_unix_timestamp(${src_field}, \'${datetime_format}\')-to_unix_timestamp(\'${begin_time}\', \'${datetime_format}\') >= 0) AND (${src_filter}) ', 'timeliness_items', 0, 1, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('12', 1, 'SELECT COUNT(*) AS timeliness FROM timeliness_items', 'timeliness_count', 1, 0, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('13', 1, 'SELECT * FROM ${src_table} where (${src_field} not in ( ${enum_list} ) or ${src_field} is null) AND (${src_filter}) ', 'enum_items', 0, 1, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('14', 1, 'SELECT COUNT(*) AS enums FROM enum_items', 'enum_count', 1, 0, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('15', 1, 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'table_count', 1, 0, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('16', 1, 'SELECT * FROM ${src_table} WHERE (${src_field} is null or ${src_field} = \'\') AND (${src_filter})', 'null_items', 0, 1, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('17', 1, 'SELECT * FROM ${src_table} WHERE (length(${src_field}) ${logic_operator} ${field_length}) AND (${src_filter})', 'invalid_length_items', 0, 1, '2022-10-13 10:18:13', '2022-10-13 10:18:13');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('2', 1, 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'total_count', 2, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('3', 1, 'SELECT COUNT(*) AS miss from miss_items', 'miss_count', 1, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('4', 1, 'SELECT COUNT(*) AS valids FROM invalid_length_items', 'invalid_length_count', 1, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('5', 1, 'SELECT COUNT(*) AS total FROM ${target_table} WHERE (${target_filter})', 'total_count', 2, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('6', 1, 'SELECT ${src_field} FROM ${src_table} group by ${src_field} having count(*) > 1', 'duplicate_items', 0, 1, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('7', 1, 'SELECT COUNT(*) AS duplicates FROM duplicate_items', 'duplicate_count', 1, 0, '2022-10-13 10:18:11', '2022-10-13 10:18:11');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('8', 1, 'SELECT ${src_table}.* FROM (SELECT * FROM ${src_table} WHERE (${src_filter})) ${src_table} LEFT JOIN (SELECT * FROM ${target_table} WHERE (${target_filter})) ${target_table} ON ${on_clause} WHERE ${where_clause}', 'miss_items', 0, 1, '2022-10-13 10:18:12', '2022-10-13 10:18:12');
INSERT INTO `t_ds_dq_rule_execute_sql` VALUES ('9', 1, 'SELECT * FROM ${src_table} WHERE (${src_field} not regexp \'${regexp_pattern}\') AND (${src_filter}) ', 'regexp_items', 0, 1, '2022-10-13 10:18:12', '2022-10-13 10:18:12');

-- ----------------------------
-- Table structure for t_ds_dq_rule_input_entry
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_rule_input_entry`;
CREATE TABLE `t_ds_dq_rule_input_entry`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `field` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `options` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `placeholder` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `option_source_type` int(0) NULL DEFAULT NULL,
  `value_type` int(0) NULL DEFAULT NULL,
  `input_type` int(0) NULL DEFAULT NULL,
  `is_show` tinyint(1) NULL DEFAULT 1,
  `can_edit` tinyint(1) NULL DEFAULT 1,
  `is_emit` tinyint(1) NULL DEFAULT 0,
  `is_validate` tinyint(1) NULL DEFAULT 1,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_rule_input_entry
-- ----------------------------
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('1', 'src_connector_type', 'select', '$t(src_connector_type)', '', '[{\"label\":\"HIVE\",\"value\":\"HIVE\"},{\"label\":\"JDBC\",\"value\":\"JDBC\"}]', 'please select source connector type', 2, 2, 0, 1, 1, 1, 0, '2022-10-13 10:18:13', '2022-10-13 10:18:13');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('10', 'failure_strategy', 'select', '$t(failure_strategy)', '0', '[{\"label\":\"Alert\",\"value\":\"0\"},{\"label\":\"Block\",\"value\":\"1\"}]', 'please select failure strategy', 0, 0, 3, 1, 1, 0, 0, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('11', 'target_connector_type', 'select', '$t(target_connector_type)', '', '[{\"label\":\"HIVE\",\"value\":\"HIVE\"},{\"label\":\"JDBC\",\"value\":\"JDBC\"}]', 'Please select target connector type', 2, 0, 0, 1, 1, 1, 0, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('12', 'target_datasource_id', 'select', '$t(target_datasource_id)', '', NULL, 'Please select target datasource', 1, 2, 0, 1, 1, 1, 0, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('13', 'target_table', 'select', '$t(target_table)', NULL, NULL, 'Please enter target table', 0, 0, 0, 1, 1, 1, 1, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('14', 'target_filter', 'input', '$t(target_filter)', NULL, NULL, 'Please enter target filter expression', 0, 3, 0, 1, 1, 0, 0, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('15', 'mapping_columns', 'group', '$t(mapping_columns)', NULL, '[{\"field\":\"src_field\",\"props\":{\"placeholder\":\"Please input src field\",\"rows\":0,\"disabled\":false,\"size\":\"small\"},\"type\":\"input\",\"title\":\"src_field\"},{\"field\":\"operator\",\"props\":{\"placeholder\":\"Please input operator\",\"rows\":0,\"disabled\":false,\"size\":\"small\"},\"type\":\"input\",\"title\":\"operator\"},{\"field\":\"target_field\",\"props\":{\"placeholder\":\"Please input target field\",\"rows\":0,\"disabled\":false,\"size\":\"small\"},\"type\":\"input\",\"title\":\"target_field\"}]', 'please enter mapping columns', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('16', 'statistics_execute_sql', 'textarea', '$t(statistics_execute_sql)', NULL, NULL, 'Please enter statistics execute sql', 0, 3, 0, 1, 1, 0, 1, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('17', 'comparison_name', 'input', '$t(comparison_name)', NULL, NULL, 'Please enter comparison name, the alias in comparison execute sql', 0, 0, 0, 0, 0, 0, 1, '2022-10-13 10:18:15', '2022-10-13 10:18:15');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('18', 'comparison_execute_sql', 'textarea', '$t(comparison_execute_sql)', NULL, NULL, 'Please enter comparison execute sql', 0, 3, 0, 1, 1, 0, 1, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('19', 'comparison_type', 'select', '$t(comparison_type)', '', NULL, 'Please enter comparison title', 3, 0, 2, 1, 0, 1, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('2', 'src_datasource_id', 'select', '$t(src_datasource_id)', '', NULL, 'please select source datasource id', 1, 2, 0, 1, 1, 1, 0, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('20', 'writer_connector_type', 'select', '$t(writer_connector_type)', '', '[{\"label\":\"MYSQL\",\"value\":\"0\"},{\"label\":\"POSTGRESQL\",\"value\":\"1\"}]', 'please select writer connector type', 0, 2, 0, 1, 1, 1, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('21', 'writer_datasource_id', 'select', '$t(writer_datasource_id)', '', NULL, 'please select writer datasource id', 1, 2, 0, 1, 1, 0, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('22', 'target_field', 'select', '$t(target_field)', NULL, NULL, 'Please enter column, only single column is supported', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('23', 'field_length', 'input', '$t(field_length)', NULL, NULL, 'Please enter length limit', 0, 3, 0, 1, 1, 0, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('24', 'logic_operator', 'select', '$t(logic_operator)', '=', '[{\"label\":\"=\",\"value\":\"=\"},{\"label\":\"<\",\"value\":\"<\"},{\"label\":\"<=\",\"value\":\"<=\"},{\"label\":\">\",\"value\":\">\"},{\"label\":\">=\",\"value\":\">=\"},{\"label\":\"<>\",\"value\":\"<>\"}]', 'please select logic operator', 0, 0, 3, 1, 1, 0, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('25', 'regexp_pattern', 'input', '$t(regexp_pattern)', NULL, NULL, 'Please enter regexp pattern', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:16', '2022-10-13 10:18:16');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('26', 'deadline', 'input', '$t(deadline)', NULL, NULL, 'Please enter deadline', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:17', '2022-10-13 10:18:17');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('27', 'datetime_format', 'input', '$t(datetime_format)', NULL, NULL, 'Please enter datetime format', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:17', '2022-10-13 10:18:17');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('28', 'enum_list', 'input', '$t(enum_list)', NULL, NULL, 'Please enter enumeration', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:17', '2022-10-13 10:18:17');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('29', 'begin_time', 'input', '$t(begin_time)', NULL, NULL, 'Please enter begin time', 0, 0, 0, 1, 1, 0, 0, '2022-10-13 10:18:17', '2022-10-13 10:18:17');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('3', 'src_table', 'select', '$t(src_table)', NULL, NULL, 'Please enter source table name', 0, 0, 0, 1, 1, 1, 1, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('4', 'src_filter', 'input', '$t(src_filter)', NULL, NULL, 'Please enter filter expression', 0, 3, 0, 1, 1, 0, 0, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('5', 'src_field', 'select', '$t(src_field)', NULL, NULL, 'Please enter column, only single column is supported', 0, 0, 0, 1, 1, 0, 1, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('6', 'statistics_name', 'input', '$t(statistics_name)', NULL, NULL, 'Please enter statistics name, the alias in statistics execute sql', 0, 0, 1, 0, 0, 0, 1, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('7', 'check_type', 'select', '$t(check_type)', '0', '[{\"label\":\"Expected - Actual\",\"value\":\"0\"},{\"label\":\"Actual - Expected\",\"value\":\"1\"},{\"label\":\"Actual / Expected\",\"value\":\"2\"},{\"label\":\"(Expected - Actual) / Expected\",\"value\":\"3\"}]', 'please select check type', 0, 0, 3, 1, 1, 1, 0, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('8', 'operator', 'select', '$t(operator)', '0', '[{\"label\":\"=\",\"value\":\"0\"},{\"label\":\"<\",\"value\":\"1\"},{\"label\":\"<=\",\"value\":\"2\"},{\"label\":\">\",\"value\":\"3\"},{\"label\":\">=\",\"value\":\"4\"},{\"label\":\"!=\",\"value\":\"5\"}]', 'please select operator', 0, 0, 3, 1, 1, 0, 0, '2022-10-13 10:18:14', '2022-10-13 10:18:14');
INSERT INTO `t_ds_dq_rule_input_entry` VALUES ('9', 'threshold', 'input', '$t(threshold)', NULL, NULL, 'Please enter threshold, number is needed', 0, 2, 3, 1, 1, 0, 1, '2022-10-13 10:18:14', '2022-10-13 10:18:14');

-- ----------------------------
-- Table structure for t_ds_dq_task_statistics_value
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_dq_task_statistics_value`;
CREATE TABLE `t_ds_dq_task_statistics_value`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `unique_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `statistics_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `statistics_value` double NULL DEFAULT NULL,
  `data_time` datetime(0) NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_dq_task_statistics_value
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_environment
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_environment`;
CREATE TABLE `t_ds_environment`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint(0) NULL DEFAULT NULL COMMENT 'encoding',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'environment name',
  `config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'this config contains many environment variables config',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'the details',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `environment_name_unique`(`name`) USING BTREE,
  UNIQUE INDEX `environment_code_unique`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_environment
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_environment_worker_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_environment_worker_group_relation`;
CREATE TABLE `t_ds_environment_worker_group_relation`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `environment_code` bigint(0) NOT NULL COMMENT 'environment code',
  `worker_group` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'worker group id',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `environment_worker_group_unique`(`environment_code`, `worker_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_environment_worker_group_relation
-- ----------------------------
INSERT INTO `t_ds_environment_worker_group_relation` VALUES ('7901586607552', 7901586592576, 'default', '1', '2022-12-16 11:32:25', '2022-12-16 11:32:25');

-- ----------------------------
-- Table structure for t_ds_error_command
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_error_command`;
CREATE TABLE `t_ds_error_command`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `command_type` tinyint(0) NULL DEFAULT NULL COMMENT 'command type',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'executor id',
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process definition code',
  `process_definition_version` int(0) NULL DEFAULT 0 COMMENT 'process definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT 'process instance id: 0',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'json command parameters',
  `task_depend_type` tinyint(0) NULL DEFAULT NULL COMMENT 'task depend type',
  `failure_strategy` tinyint(0) NULL DEFAULT 0 COMMENT 'failure strategy',
  `warning_type` tinyint(0) NULL DEFAULT 0 COMMENT 'warning type',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'warning group id',
  `schedule_time` datetime(0) NULL DEFAULT NULL COMMENT 'scheduler time',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT 'start time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `process_instance_priority` int(0) NULL DEFAULT 2 COMMENT 'process instance priority, 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker group',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'message',
  `dry_run` tinyint(0) NULL DEFAULT 0 COMMENT 'dry run flag: 0 normal, 1 dry run',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_error_command
-- ----------------------------
INSERT INTO `t_ds_error_command` VALUES ('11199372892448', 0, '4', 11199358291360, 0, '0', '{}', 2, 1, 0, '0', NULL, '2023-10-10 16:11:41', '2023-10-10 16:11:41', 2, NULL, -1, 'org.apache.dolphinscheduler.service.exceptions.ServiceException: delete command fail, id:11199372892448', 0);
INSERT INTO `t_ds_error_command` VALUES ('11199387891104', 0, '4', 11199358291360, 0, '0', '{}', 2, 1, 0, '0', NULL, '2023-10-10 16:13:38', '2023-10-10 16:13:38', 2, NULL, -1, 'org.apache.dolphinscheduler.service.exceptions.ServiceException: delete command fail, id:11199387891104', 0);

-- ----------------------------
-- Table structure for t_ds_fav_task
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_fav_task`;
CREATE TABLE `t_ds_fav_task`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `task_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'favorite task name',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_fav_task
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_k8s
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_k8s`;
CREATE TABLE `t_ds_k8s`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `k8s_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `k8s_config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_k8s
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_k8s_namespace
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_k8s_namespace`;
CREATE TABLE `t_ds_k8s_namespace`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint(0) NOT NULL DEFAULT 0,
  `limits_memory` int(0) NULL DEFAULT NULL,
  `namespace` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pod_replicas` int(0) NULL DEFAULT NULL,
  `pod_request_cpu` decimal(14, 3) NULL DEFAULT NULL,
  `pod_request_memory` int(0) NULL DEFAULT NULL,
  `limits_cpu` decimal(14, 3) NULL DEFAULT NULL,
  `cluster_code` bigint(0) NOT NULL DEFAULT 0,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `k8s_namespace_unique`(`namespace`, `cluster_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_k8s_namespace
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_mysql_registry_data
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_mysql_registry_data`;
CREATE TABLE `t_ds_mysql_registry_data`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `key` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'key, like zookeeper node path',
  `data` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'data, like zookeeper node value',
  `type` tinyint(0) NOT NULL COMMENT '1: ephemeral node, 2: persistent node',
  `last_update_time` timestamp(0) NULL DEFAULT NULL COMMENT 'last update time',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT 'create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `key`(`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_mysql_registry_data
-- ----------------------------
INSERT INTO `t_ds_mysql_registry_data` VALUES ('235', '/nodes/master', '', 2, '2022-10-15 11:04:38', '2022-10-13 15:08:49');
INSERT INTO `t_ds_mysql_registry_data` VALUES ('236', '/nodes/worker', '', 2, '2022-10-15 11:04:39', '2022-10-13 15:08:49');

-- ----------------------------
-- Table structure for t_ds_mysql_registry_lock
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_mysql_registry_lock`;
CREATE TABLE `t_ds_mysql_registry_lock`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `key` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'lock path',
  `lock_owner` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the lock owner, ip_processId',
  `last_term` timestamp(0) NOT NULL COMMENT 'last term time',
  `last_update_time` timestamp(0) NULL DEFAULT NULL COMMENT 'last update time',
  `create_time` timestamp(0) NULL DEFAULT NULL COMMENT 'lock create time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `key`(`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_mysql_registry_lock
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_plugin_define
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_plugin_define`;
CREATE TABLE `t_ds_plugin_define`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `plugin_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'the name of plugin eg: email',
  `plugin_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'plugin type . alert=alert plugin, job=job plugin',
  `plugin_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'plugin params',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `t_ds_plugin_define_UN`(`plugin_name`, `plugin_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_plugin_define
-- ----------------------------
INSERT INTO `t_ds_plugin_define` VALUES ('10', 'SHELL', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:47:58', '2022-10-13 10:47:58');
INSERT INTO `t_ds_plugin_define` VALUES ('11', 'DEPENDENT', 'task', 'null', '2022-10-13 10:47:58', '2022-10-13 10:47:58');
INSERT INTO `t_ds_plugin_define` VALUES ('12', 'MR', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('13', 'SQOOP', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('14', 'SUB_PROCESS', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('15', 'PYTORCH', 'task', '[]', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('16', 'K8S', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('17', 'SEATUNNEL', 'task', 'null', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('18', 'SAGEMAKER', 'task', '[]', '2022-10-13 10:47:59', '2022-10-13 10:47:59');
INSERT INTO `t_ds_plugin_define` VALUES ('19', 'HTTP', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('2', 'JUPYTER', 'task', 'null', '2022-10-13 10:47:56', '2022-10-13 10:47:56');
INSERT INTO `t_ds_plugin_define` VALUES ('20', 'EMR', 'task', '[]', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('21', 'DATA_QUALITY', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('22', 'SQL', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('23', 'DVC', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('24', 'DATAX', 'task', 'null', '2022-10-13 10:48:00', '2022-10-13 10:48:00');
INSERT INTO `t_ds_plugin_define` VALUES ('25', 'ZEPPELIN', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('26', 'DINKY', 'task', '[]', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('27', 'MLFLOW', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('28', 'SWITCH', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('29', 'OPENMLDB', 'task', 'null', '2022-10-13 10:48:01', '2022-10-13 10:48:01');
INSERT INTO `t_ds_plugin_define` VALUES ('3', 'SPARK', 'task', 'null', '2022-10-13 10:47:56', '2022-10-13 10:47:56');
INSERT INTO `t_ds_plugin_define` VALUES ('30', 'BLOCKING', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('31', 'FLINK', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('32', 'HIVECLI', 'task', 'null', '2022-10-13 10:48:02', '2022-10-13 10:48:02');
INSERT INTO `t_ds_plugin_define` VALUES ('4', 'FLINK_STREAM', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('5', 'PYTHON', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('6', 'CHUNJUN', 'task', 'null', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('7', 'CONDITIONS', 'task', '[{\"props\":null,\"field\":\"name\",\"name\":\"$t(\'Node name\')\",\"type\":\"input\",\"title\":\"$t(\'Node name\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"runFlag\",\"name\":\"RUN_FLAG\",\"type\":\"radio\",\"title\":\"RUN_FLAG\",\"value\":null,\"validate\":null,\"emit\":null,\"options\":[{\"label\":\"NORMAL\",\"value\":\"NORMAL\",\"disabled\":false},{\"label\":\"FORBIDDEN\",\"value\":\"FORBIDDEN\",\"disabled\":false}]}]', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662198464', 'Script', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please enter your custom parameters, which will be passed to you when calling your script\",\"size\":\"small\"},\"field\":\"userParams\",\"name\":\"$t(\'userParams\')\",\"type\":\"input\",\"title\":\"$t(\'userParams\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please upload the file to the disk directory of the alert server, and ensure that the path is absolute and has the corresponding access rights\",\"size\":\"small\"},\"field\":\"path\",\"name\":\"$t(\'scriptPath\')\",\"type\":\"input\",\"title\":\"$t(\'scriptPath\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"type\",\"name\":\"$t(\'scriptType\')\",\"type\":\"radio\",\"title\":\"$t(\'scriptType\')\",\"value\":\"SHELL\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"SHELL\",\"value\":\"SHELL\",\"disabled\":false}]}]', '2022-12-09 16:56:38', '2022-12-09 16:56:38');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662217792', 'WeChat', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input corp id \",\"size\":\"small\"},\"field\":\"corpId\",\"name\":\"$t(\'corpId\')\",\"type\":\"input\",\"title\":\"$t(\'corpId\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input secret \",\"size\":\"small\"},\"field\":\"secret\",\"name\":\"$t(\'secret\')\",\"type\":\"input\",\"title\":\"$t(\'secret\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"use `|` to separate userIds and `@all` to everyone \",\"size\":\"small\"},\"field\":\"users\",\"name\":\"$t(\'users\')\",\"type\":\"input\",\"title\":\"$t(\'users\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input agent id or chat id \",\"size\":\"small\"},\"field\":\"agentId/chatId\",\"name\":\"$t(\'agentId/chatId\')\",\"type\":\"input\",\"title\":\"$t(\'agentId/chatId\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"sendType\",\"name\":\"send.type\",\"type\":\"radio\",\"title\":\"send.type\",\"value\":\"APP/应用\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"APP/应用\",\"value\":\"APP/应用\",\"disabled\":false},{\"label\":\"GROUP CHAT/群聊\",\"value\":\"GROUP CHAT/群聊\",\"disabled\":false}]},{\"props\":null,\"field\":\"showType\",\"name\":\"$t(\'showType\')\",\"type\":\"radio\",\"title\":\"$t(\'showType\')\",\"value\":\"markdown\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"markdown\",\"value\":\"markdown\",\"disabled\":false},{\"label\":\"text\",\"value\":\"text\",\"disabled\":false}]}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662238912', 'Telegram', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram web hook\",\"size\":\"small\"},\"field\":\"webHook\",\"name\":\"$t(\'webHook\')\",\"type\":\"input\",\"title\":\"$t(\'webHook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram bot token\",\"size\":\"small\"},\"field\":\"botToken\",\"name\":\"botToken\",\"type\":\"input\",\"title\":\"botToken\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"telegram channel chat id\",\"size\":\"small\"},\"field\":\"chatId\",\"name\":\"chatId\",\"type\":\"input\",\"title\":\"chatId\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"parseMode\",\"name\":\"parseMode\",\"props\":{\"disabled\":null,\"placeholder\":null,\"size\":\"small\"},\"type\":\"select\",\"title\":\"parseMode\",\"value\":\"Txt\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"Txt\",\"value\":\"Txt\",\"disabled\":false},{\"label\":\"Markdown\",\"value\":\"Markdown\",\"disabled\":false},{\"label\":\"MarkdownV2\",\"value\":\"MarkdownV2\",\"disabled\":false},{\"label\":\"Html\",\"value\":\"Html\",\"disabled\":false}]},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662254912', 'Email', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"please input receives\",\"size\":\"small\"},\"field\":\"receivers\",\"name\":\"$t(\'receivers\')\",\"type\":\"input\",\"title\":\"$t(\'receivers\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"receiverCcs\",\"name\":\"$t(\'receiverCcs\')\",\"type\":\"input\",\"title\":\"$t(\'receiverCcs\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"props\":null,\"field\":\"serverHost\",\"name\":\"mail.smtp.host\",\"type\":\"input\",\"title\":\"mail.smtp.host\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"serverPort\",\"name\":\"mail.smtp.port\",\"type\":\"input\",\"title\":\"mail.smtp.port\",\"value\":\"25\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"sender\",\"name\":\"$t(\'mailSender\')\",\"type\":\"input\",\"title\":\"$t(\'mailSender\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"enableSmtpAuth\",\"name\":\"mail.smtp.auth\",\"type\":\"radio\",\"title\":\"mail.smtp.auth\",\"value\":\"true\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"if enable use authentication, you need input user\",\"size\":\"small\"},\"field\":\"User\",\"name\":\"$t(\'mailUser\')\",\"type\":\"input\",\"title\":\"$t(\'mailUser\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'mailPasswd\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'mailPasswd\')\",\"value\":null,\"validate\":null,\"emit\":null},{\"props\":null,\"field\":\"starttlsEnable\",\"name\":\"mail.smtp.starttls.enable\",\"type\":\"radio\",\"title\":\"mail.smtp.starttls.enable\",\"value\":\"false\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"sslEnable\",\"name\":\"mail.smtp.ssl.enable\",\"type\":\"radio\",\"title\":\"mail.smtp.ssl.enable\",\"value\":\"false\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"smtpSslTrust\",\"name\":\"mail.smtp.ssl.trust\",\"type\":\"input\",\"title\":\"mail.smtp.ssl.trust\",\"value\":\"*\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"showType\",\"name\":\"$t(\'showType\')\",\"type\":\"radio\",\"title\":\"$t(\'showType\')\",\"value\":\"table\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"table\",\"value\":\"table\",\"disabled\":false},{\"label\":\"text\",\"value\":\"text\",\"disabled\":false},{\"label\":\"attachment\",\"value\":\"attachment\",\"disabled\":false},{\"label\":\"table attachment\",\"value\":\"table attachment\",\"disabled\":false}]}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662279488', 'Slack', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Input WebHook Url\",\"size\":\"small\"},\"field\":\"webHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Input the bot username\",\"size\":\"small\"},\"field\":\"username\",\"name\":\"$t(\'Username\')\",\"type\":\"input\",\"title\":\"$t(\'Username\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662320960', 'Feishu', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"WebHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"true\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":null,\"emit\":null}]', '2022-12-09 16:56:39', '2022-12-09 16:56:39');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662341824', 'Http', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request URL\",\"size\":\"small\"},\"field\":\"url\",\"name\":\"$t(\'url\')\",\"type\":\"input\",\"title\":\"$t(\'url\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request type POST or GET\",\"size\":\"small\"},\"field\":\"requestType\",\"name\":\"$t(\'requestType\')\",\"type\":\"input\",\"title\":\"$t(\'requestType\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request headers as JSON format \",\"size\":\"small\"},\"field\":\"headerParams\",\"name\":\"$t(\'headerParams\')\",\"type\":\"input\",\"title\":\"$t(\'headerParams\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input request body as JSON format \",\"size\":\"small\"},\"field\":\"bodyParams\",\"name\":\"$t(\'bodyParams\')\",\"type\":\"input\",\"title\":\"$t(\'bodyParams\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"input alert msg field name\",\"size\":\"small\"},\"field\":\"contentField\",\"name\":\"$t(\'contentField\')\",\"type\":\"input\",\"title\":\"$t(\'contentField\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662360768', 'DingTalk', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"WebHook\",\"name\":\"$t(\'webhook\')\",\"type\":\"input\",\"title\":\"$t(\'webhook\')\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Keyword\",\"name\":\"$t(\'keyword\')\",\"type\":\"input\",\"title\":\"$t(\'keyword\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Secret\",\"name\":\"$t(\'secret\')\",\"type\":\"input\",\"title\":\"$t(\'secret\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"MsgType\",\"name\":\"$t(\'msgType\')\",\"type\":\"radio\",\"title\":\"$t(\'msgType\')\",\"value\":\"text\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"text\",\"value\":\"text\",\"disabled\":false},{\"label\":\"markdown\",\"value\":\"markdown\",\"disabled\":false}]},{\"props\":null,\"field\":\"AtMobiles\",\"name\":\"$t(\'atMobiles\')\",\"type\":\"input\",\"title\":\"$t(\'atMobiles\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"AtUserIds\",\"name\":\"$t(\'atUserIds\')\",\"type\":\"input\",\"title\":\"$t(\'atUserIds\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"IsAtAll\",\"name\":\"$t(\'isAtAll\')\",\"type\":\"radio\",\"title\":\"$t(\'isAtAll\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"IsEnableProxy\",\"name\":\"$t(\'isEnableProxy\')\",\"type\":\"radio\",\"title\":\"$t(\'isEnableProxy\')\",\"value\":\"false\",\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"YES\",\"value\":\"true\",\"disabled\":false},{\"label\":\"NO\",\"value\":\"false\",\"disabled\":false}]},{\"props\":null,\"field\":\"Proxy\",\"name\":\"$t(\'proxy\')\",\"type\":\"input\",\"title\":\"$t(\'proxy\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Port\",\"name\":\"$t(\'port\')\",\"type\":\"input\",\"title\":\"$t(\'port\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"User\",\"name\":\"$t(\'user\')\",\"type\":\"input\",\"title\":\"$t(\'user\')\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"field\":\"Password\",\"name\":\"$t(\'password\')\",\"props\":{\"disabled\":null,\"placeholder\":\"if enable use authentication, you need input password\",\"size\":\"small\"},\"type\":\"input\",\"title\":\"$t(\'password\')\",\"value\":null,\"validate\":null,\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662381120', 'WebexTeams', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"Please enter the robot\'s access token you were given\",\"size\":\"small\"},\"field\":\"BotAccessToken\",\"name\":\"botAccessToken\",\"type\":\"input\",\"title\":\"botAccessToken\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The room ID of the message\",\"size\":\"small\"},\"field\":\"RoomId\",\"name\":\"roomId\",\"type\":\"input\",\"title\":\"roomId\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The person ID of the message recipient\",\"size\":\"small\"},\"field\":\"ToPersonId\",\"name\":\"toPersonId\",\"type\":\"input\",\"title\":\"toPersonId\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"The email address of the message recipient\",\"size\":\"small\"},\"field\":\"ToPersonEmail\",\"name\":\"toPersonEmail\",\"type\":\"input\",\"title\":\"toPersonEmail\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":{\"disabled\":null,\"type\":null,\"maxlength\":null,\"minlength\":null,\"clearable\":null,\"prefixIcon\":null,\"suffixIcon\":null,\"rows\":null,\"autosize\":null,\"autocomplete\":null,\"name\":null,\"readonly\":null,\"max\":null,\"min\":null,\"step\":null,\"resize\":null,\"autofocus\":null,\"form\":null,\"label\":null,\"tabindex\":null,\"validateEvent\":null,\"showPassword\":null,\"placeholder\":\"use ,(eng commas) to separate multiple emails\",\"size\":\"small\"},\"field\":\"AtSomeoneInRoom\",\"name\":\"atSomeoneInRoom\",\"type\":\"input\",\"title\":\"atSomeoneInRoom\",\"value\":null,\"validate\":[{\"required\":false,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null},{\"props\":null,\"field\":\"Destination\",\"name\":\"destination\",\"type\":\"radio\",\"title\":\"destination\",\"value\":\"roomId\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"roomId\",\"value\":\"roomId\",\"disabled\":false},{\"label\":\"personEmail\",\"value\":\"personEmail\",\"disabled\":false},{\"label\":\"personId\",\"value\":\"personId\",\"disabled\":false}]}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('7826662402752', 'PagerDuty', 'alert', '[{\"props\":null,\"field\":\"WarningType\",\"name\":\"warningType\",\"type\":\"radio\",\"title\":\"warningType\",\"value\":\"all\",\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null,\"options\":[{\"label\":\"success\",\"value\":\"success\",\"disabled\":false},{\"label\":\"failure\",\"value\":\"failure\",\"disabled\":false},{\"label\":\"all\",\"value\":\"all\",\"disabled\":false}]},{\"props\":null,\"field\":\"IntegrationKey\",\"name\":\"integrationKey\",\"type\":\"input\",\"title\":\"integrationKey\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-12-09 16:56:40', '2022-12-09 16:56:40');
INSERT INTO `t_ds_plugin_define` VALUES ('8', 'PIGEON', 'task', '[{\"props\":null,\"field\":\"targetJobName\",\"name\":\"targetJobName\",\"type\":\"input\",\"title\":\"targetJobName\",\"value\":null,\"validate\":[{\"required\":true,\"message\":null,\"type\":\"string\",\"trigger\":\"blur\",\"min\":null,\"max\":null}],\"emit\":null}]', '2022-10-13 10:47:57', '2022-10-13 10:47:57');
INSERT INTO `t_ds_plugin_define` VALUES ('9', 'PROCEDURE', 'task', 'null', '2022-10-13 10:47:58', '2022-10-13 10:47:58');

-- ----------------------------
-- Table structure for t_ds_process_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_process_definition`;
CREATE TABLE `t_ds_process_definition`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process definition name',
  `version` int(0) NULL DEFAULT 0 COMMENT 'process definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `release_state` tinyint(0) NULL DEFAULT NULL COMMENT 'process definition release state：0:offline,1:online',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process definition creator id',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'global parameters',
  `flag` tinyint(0) NULL DEFAULT NULL COMMENT '0 not available, 1 available',
  `locations` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'Node location information',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert group id',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'time out, unit: minute',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `execution_type` tinyint(0) NULL DEFAULT 0 COMMENT 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`, `code`) USING BTREE,
  UNIQUE INDEX `process_unique`(`name`, `project_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_process_definition
-- ----------------------------
INSERT INTO `t_ds_process_definition` VALUES ('10811931144640', 10811931125184, '请求百度', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10811878809280,\"x\":303.35064697265625,\"y\":338.9131774902344}]', NULL, 0, '-1', 0, '2023-09-05 15:23:32', '2023-09-05 15:23:32');
INSERT INTO `t_ds_process_definition` VALUES ('10812439542976', 10812439524545, '自定义接口测试', 3, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":10812436419776,\"x\":240,\"y\":230}]', NULL, 0, '-1', 0, '2023-09-05 16:29:44', '2023-09-05 16:41:17');
INSERT INTO `t_ds_process_definition` VALUES ('10812679603008', 10812679584833, 'shell', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":10812663635136,\"x\":187.35064697265625,\"y\":127.91319274902344}]', NULL, 0, '-1', 0, '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_process_definition` VALUES ('10821251791808', 10821251772608, '测试bat', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":10821244252096,\"x\":269.35064697265625,\"y\":149.91319274902344}]', NULL, 0, '-1', 0, '2023-09-06 11:37:09', '2023-09-06 11:37:09');
INSERT INTO `t_ds_process_definition` VALUES ('10835095050432', 10835095030848, 'datax', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":10835090078656,\"x\":224.35064697265625,\"y\":355.9131774902344}]', NULL, 0, '-1', 0, '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_process_definition` VALUES ('10844574549312', 10844574533184, 'shell输出DAG', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":10844570462400,\"x\":259.765625,\"y\":188.25390625}]', NULL, 0, '-1', 0, '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_process_definition` VALUES ('11178114508832', 11178114471456, '测试新增', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":11177851623200,\"x\":135,\"y\":45}]', NULL, 0, '-1', 0, '2023-10-08 18:03:39', '2023-10-08 18:03:39');
INSERT INTO `t_ds_process_definition` VALUES ('11196578130496', 11196578111424, 'ST测试', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":195.35064697265625,\"y\":238.91317749023438}]', NULL, 0, '-1', 0, '2023-10-10 10:07:46', '2023-10-10 10:07:46');
INSERT INTO `t_ds_process_definition` VALUES ('11198719124416', 11198719105088, 'st01', 4, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":330,\"y\":340}]', NULL, 0, '-1', 0, '2023-10-10 14:46:33', '2023-10-10 14:57:01');
INSERT INTO `t_ds_process_definition` VALUES ('11199358293664', 11199358291360, 'st_linux', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":252,\"y\":148}]', NULL, 0, '-1', 0, '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_process_definition` VALUES ('11199601129504', 11199601127968, 'st_cluster', 1, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":123,\"y\":265.3999938964844}]', NULL, 0, '-1', 0, '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_process_definition` VALUES ('11199702771264', 11199702752064, '0001', 1, '', 11199696680000, 0, '4', '[]', 1, '[{\"taskCode\":11199699252928,\"x\":349.22222900390625,\"y\":346.22222900390625}]', NULL, 0, '-1', 0, '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_process_definition` VALUES ('11199720136384', 11199720118592, 's001', 1, '', 11199696680000, 1, '4', '[]', 1, '[{\"taskCode\":11199717663680,\"x\":239.22222900390625,\"y\":113}]', NULL, 0, '-1', 0, '2023-10-10 16:56:53', '2023-10-10 16:56:53');
INSERT INTO `t_ds_process_definition` VALUES ('11276197989536', 11276197971872, '测试定时工作流新增', 16, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition` VALUES ('11421193801664', 11421193780800, 'ces', 6, '', 11199696680000, 1, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90}]', NULL, 0, '-1', 0, '2023-10-30 17:34:36', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_definition` VALUES ('11421317267392', 11421317248066, 'dag01', 1, '', 11199696680000, 1, '1', '[]', 1, '[{\"taskCode\":11421313640256,\"x\":120,\"y\":120},{\"taskCode\":11421314732992,\"x\":475.22222900390625,\"y\":144.11111450195312}]', NULL, 0, '-1', 0, '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_definition` VALUES ('11508998348352', 11508998330944, 'dag01', 6, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '2023-11-07 16:07:29', '2023-11-07 18:07:55');

-- ----------------------------
-- Table structure for t_ds_process_definition_log
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_process_definition_log`;
CREATE TABLE `t_ds_process_definition_log`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process definition name',
  `version` int(0) NULL DEFAULT 0 COMMENT 'process definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `release_state` tinyint(0) NULL DEFAULT NULL COMMENT 'process definition release state：0:offline,1:online',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process definition creator id',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'global parameters',
  `flag` tinyint(0) NULL DEFAULT NULL COMMENT '0 not available, 1 available',
  `locations` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'Node location information',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert group id',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'time out,unit: minute',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `execution_type` tinyint(0) NULL DEFAULT 0 COMMENT 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `operate_time` datetime(0) NULL DEFAULT NULL COMMENT 'operate time',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_process_definition_log
-- ----------------------------
INSERT INTO `t_ds_process_definition_log` VALUES ('10811931144640', 10811931125184, '请求百度', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10811878809280,\"x\":303.35064697265625,\"y\":338.9131774902344}]', NULL, 0, '-1', 0, '4', '2023-09-05 15:23:32', '2023-09-05 15:23:32', '2023-09-05 15:23:32');
INSERT INTO `t_ds_process_definition_log` VALUES ('10812427272256', 10812427252288, '自定义接口调用', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":10812422708672,\"x\":298.22222900390625,\"y\":219}]', NULL, 0, '-1', 0, '1', '2023-09-05 16:28:08', '2023-09-05 16:28:08', '2023-09-05 16:28:08');
INSERT INTO `t_ds_process_definition_log` VALUES ('10812439542976', 10812439524545, '自定义接口测试', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10812436419776,\"x\":236.22222900390625,\"y\":231}]', NULL, 0, '-1', 0, '4', '2023-09-05 16:29:44', '2023-09-05 16:29:44', '2023-09-05 16:29:44');
INSERT INTO `t_ds_process_definition_log` VALUES ('10812452091968', 10812439524545, '自定义接口测试', 2, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10812436419776,\"x\":236.22222900390625,\"y\":231}]', NULL, 0, '-1', 0, '4', '2023-09-05 16:31:22', '2023-09-05 16:29:44', '2023-09-05 16:31:22');
INSERT INTO `t_ds_process_definition_log` VALUES ('10812528312896', 10812439524545, '自定义接口测试', 3, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10812436419776,\"x\":240,\"y\":230}]', NULL, 0, '-1', 0, '4', '2023-09-05 16:41:17', '2023-09-05 16:29:44', '2023-09-05 16:41:17');
INSERT INTO `t_ds_process_definition_log` VALUES ('10812679603008', 10812679584833, 'shell', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10812663635136,\"x\":187.35064697265625,\"y\":127.91319274902344}]', NULL, 0, '-1', 0, '4', '2023-09-05 17:00:59', '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_process_definition_log` VALUES ('10821251791808', 10821251772608, '测试bat', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10821244252096,\"x\":269.35064697265625,\"y\":149.91319274902344}]', NULL, 0, '-1', 0, '4', '2023-09-06 11:37:09', '2023-09-06 11:37:09', '2023-09-06 11:37:09');
INSERT INTO `t_ds_process_definition_log` VALUES ('10835095050432', 10835095030848, 'datax', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10835090078656,\"x\":224.35064697265625,\"y\":355.9131774902344}]', NULL, 0, '-1', 0, '4', '2023-09-07 17:39:40', '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_process_definition_log` VALUES ('10844574549312', 10844574533184, 'shell输出DAG', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":10844570462400,\"x\":259.765625,\"y\":188.25390625}]', NULL, 0, '-1', 0, '4', '2023-09-08 14:13:59', '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_process_definition_log` VALUES ('10989190413760', 10989190396736, '测试01', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":10989164532800,\"x\":353.35064697265625,\"y\":229.0242919921875}]', NULL, 0, '-1', 0, '1', '2023-09-21 16:04:10', '2023-09-21 16:04:10', '2023-09-21 16:04:10');
INSERT INTO `t_ds_process_definition_log` VALUES ('10989698843072', 10989552944256, '测试001', 1, '', **************, 0, '1', '[]', 1, '', NULL, 0, '-1', 0, '1', '2023-09-21 17:10:22', '2023-09-21 17:10:22', '2023-09-21 17:10:22');
INSERT INTO `t_ds_process_definition_log` VALUES ('10989785308736', 10989552944256, '测试001', 2, '', **************, 0, '1', '[]', 1, '', NULL, 0, '-1', 0, '1', '2023-09-21 17:21:38', '2023-09-21 17:21:38', '2023-09-21 17:21:38');
INSERT INTO `t_ds_process_definition_log` VALUES ('10989976736704', 10989552944256, '测试001', 3, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-09-21 17:46:33', '2023-09-21 17:46:33', '2023-09-21 17:46:33');
INSERT INTO `t_ds_process_definition_log` VALUES ('10989999826752', 10989552944256, '测试001', 4, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-09-21 17:49:33', '2023-09-21 17:49:33', '2023-09-21 17:49:33');
INSERT INTO `t_ds_process_definition_log` VALUES ('10990031175744', 10989552944256, '测试001', 5, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-09-21 17:53:38', '2023-09-21 17:53:38', '2023-09-21 17:53:38');
INSERT INTO `t_ds_process_definition_log` VALUES ('10990046040896', 10989552944256, '测试001', 6, NULL, **************, 0, '4', '[]', 1, NULL, NULL, 0, '-1', 0, '4', '2023-09-21 17:55:35', '2023-09-21 17:53:38', '2023-09-21 17:55:35');
INSERT INTO `t_ds_process_definition_log` VALUES ('10990092229440', 10989552944256, '测试001', 7, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-09-21 18:01:35', '2023-09-21 18:01:35', '2023-09-21 18:01:35');
INSERT INTO `t_ds_process_definition_log` VALUES ('11177868365856', 11177864412192, '测试新增', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":11177851623200,\"x\":135,\"y\":45}]', NULL, 0, '-1', 0, '1', '2023-10-08 17:31:08', '2023-10-08 17:31:08', '2023-10-08 17:31:08');
INSERT INTO `t_ds_process_definition_log` VALUES ('11177925293984', 11177919249312, '测试新增', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":11177851623200,\"x\":135,\"y\":45}]', NULL, 0, '-1', 0, '1', '2023-10-08 17:38:19', '2023-10-08 17:38:19', '2023-10-08 17:38:19');
INSERT INTO `t_ds_process_definition_log` VALUES ('11178114508832', 11178114471456, '测试新增', 1, '', **************, 0, '1', '[]', 1, '[{\"taskCode\":11177851623200,\"x\":135,\"y\":45}]', NULL, 0, '-1', 0, '1', '2023-10-08 18:03:39', '2023-10-08 18:03:39', '2023-10-08 18:03:39');
INSERT INTO `t_ds_process_definition_log` VALUES ('11196578130496', 11196578111424, 'ST测试', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":195.35064697265625,\"y\":238.91317749023438}]', NULL, 0, '-1', 0, '4', '2023-10-10 10:07:46', '2023-10-10 10:07:46', '2023-10-10 10:07:46');
INSERT INTO `t_ds_process_definition_log` VALUES ('11198719124416', 11198719105088, 'st01', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":330,\"y\":340}]', NULL, 0, '-1', 0, '4', '2023-10-10 14:46:33', '2023-10-10 14:46:33', '2023-10-10 14:46:33');
INSERT INTO `t_ds_process_definition_log` VALUES ('11198794459840', 11198719105088, 'st01', 2, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":330,\"y\":340}]', NULL, 0, '-1', 0, '4', '2023-10-10 14:56:22', '2023-10-10 14:46:33', '2023-10-10 14:56:22');
INSERT INTO `t_ds_process_definition_log` VALUES ('11198795630272', 11198719105088, 'st01', 3, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":330,\"y\":340}]', NULL, 0, '-1', 0, '4', '2023-10-10 14:56:31', '2023-10-10 14:46:33', '2023-10-10 14:56:31');
INSERT INTO `t_ds_process_definition_log` VALUES ('11198799529152', 11198719105088, 'st01', 4, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":330,\"y\":340}]', NULL, 0, '-1', 0, '4', '2023-10-10 14:57:01', '2023-10-10 14:46:33', '2023-10-10 14:57:01');
INSERT INTO `t_ds_process_definition_log` VALUES ('11199358293664', 11199358291360, 'st_linux', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":252,\"y\":148}]', NULL, 0, '-1', 0, '4', '2023-10-10 16:09:47', '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_process_definition_log` VALUES ('11199601129504', 11199601127968, 'st_cluster', 1, '', **************, 0, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":123,\"y\":265.3999938964844}]', NULL, 0, '-1', 0, '4', '2023-10-10 16:41:24', '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_process_definition_log` VALUES ('11199702771264', 11199702752064, '0001', 1, '', 11199696680000, 0, '4', '[]', 1, '[{\"taskCode\":11199699252928,\"x\":349.22222900390625,\"y\":346.22222900390625}]', NULL, 0, '-1', 0, '4', '2023-10-10 16:54:38', '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_process_definition_log` VALUES ('11199720136384', 11199720118592, 's001', 1, '', 11199696680000, 0, '4', '[]', 1, '[{\"taskCode\":11199717663680,\"x\":239.22222900390625,\"y\":113}]', NULL, 0, '-1', 0, '4', '2023-10-10 16:56:53', '2023-10-10 16:56:53', '2023-10-10 16:56:53');
INSERT INTO `t_ds_process_definition_log` VALUES ('11276197989536', 11276197971872, '测试定时工作流新增', 1, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '1', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342480059936', 10989552944256, '测试002', 8, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 14:45:25', '2023-10-23 14:45:25', '2023-10-23 14:45:25');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342495408032', 10989552944256, '测试003', 9, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 14:47:25', '2023-10-23 14:47:25', '2023-10-23 14:47:25');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342547047200', 10989552944256, '测试004', 10, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 14:54:09', '2023-10-23 14:54:09', '2023-10-23 14:54:09');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342565311776', 10989552944256, '测试005', 11, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 14:56:31', '2023-10-23 14:56:31', '2023-10-23 14:56:31');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342579043744', 10989552944256, '测试006', 12, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 14:58:19', '2023-10-23 14:58:19', '2023-10-23 14:58:19');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342599046176', 10989552944256, '测试007', 13, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:00:55', '2023-10-23 15:00:55', '2023-10-23 15:00:55');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342622210848', 10989552944256, '测试008', 14, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:03:56', '2023-10-23 15:03:56', '2023-10-23 15:03:56');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342635653792', 10989552944256, '测试009', 15, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:05:41', '2023-10-23 15:05:41', '2023-10-23 15:05:41');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342643965088', 10989552944256, '测试010', 16, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:06:46', '2023-10-23 15:06:46', '2023-10-23 15:06:46');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342658883104', 10989552944256, '测试011', 17, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:08:42', '2023-10-23 15:08:42', '2023-10-23 15:08:42');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342871741472', 10989552944256, '测试012', 18, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:36:25', '2023-10-23 15:36:25', '2023-10-23 15:36:25');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342895492768', 10989552944256, '测试013', 19, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:39:31', '2023-10-23 15:39:31', '2023-10-23 15:39:31');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342919839392', 10989552944256, '测试014', 20, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:42:41', '2023-10-23 15:42:41', '2023-10-23 15:42:41');
INSERT INTO `t_ds_process_definition_log` VALUES ('11342944430752', 10989552944256, '测试015', 21, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:45:53', '2023-10-23 15:45:53', '2023-10-23 15:45:53');
INSERT INTO `t_ds_process_definition_log` VALUES ('11343030129824', 10989552944256, '测试016', 22, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 15:57:03', '2023-10-23 15:57:03', '2023-10-23 15:57:03');
INSERT INTO `t_ds_process_definition_log` VALUES ('11343060967328', 10989552944256, '测试017', 23, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 16:01:04', '2023-10-23 16:01:04', '2023-10-23 16:01:04');
INSERT INTO `t_ds_process_definition_log` VALUES ('11343074581792', 10989552944256, '测试018', 24, '', **************, 0, '4', '[]', 1, '', NULL, 0, '-1', 0, '4', '2023-10-23 16:02:50', '2023-10-23 16:02:50', '2023-10-23 16:02:50');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421193801664', 11421193780800, 'ces', 1, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":100},{\"taskCode\":11421191059392,\"x\":430,\"y\":100}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:34:36', '2023-10-30 17:34:36', '2023-10-30 17:34:36');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421217356224', 11421193780800, 'ces', 2, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90},{\"taskCode\":11421196238912,\"x\":100,\"y\":240}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:37:41', '2023-10-30 17:34:36', '2023-10-30 17:37:41');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421223252160', 11421193780800, 'ces', 3, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90},{\"taskCode\":11421196238912,\"x\":100,\"y\":240}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:38:27', '2023-10-30 17:34:36', '2023-10-30 17:38:27');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421229657408', 11421193780800, 'ces', 4, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90},{\"taskCode\":11421196238912,\"x\":220,\"y\":210},{\"taskCode\":11421228664000,\"x\":612.2222290039062,\"y\":227.11111450195312}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:39:17', '2023-10-30 17:34:36', '2023-10-30 17:39:17');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421230911680', 11421193780800, 'ces', 5, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90},{\"taskCode\":11421196238912,\"x\":220,\"y\":210},{\"taskCode\":11421228664000,\"x\":600,\"y\":200}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:39:26', '2023-10-30 17:34:36', '2023-10-30 17:39:26');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421308149440', 11421193780800, 'ces', 6, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421174732096,\"x\":100,\"y\":90},{\"taskCode\":11421191059392,\"x\":430,\"y\":90}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:49:30', '2023-10-30 17:34:36', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421317267392', 11421317248066, 'dag01', 1, '', 11199696680000, 0, '1', '[]', 1, '[{\"taskCode\":11421313640256,\"x\":120,\"y\":120},{\"taskCode\":11421314732992,\"x\":475.22222900390625,\"y\":144.11111450195312}]', NULL, 0, '-1', 0, '1', '2023-10-30 17:50:41', '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_definition_log` VALUES ('11421436822592', 11199601127968, 'st_cluster', 2, '', **************, 1, '4', '[]', 1, '[{\"taskCode\":**************,\"x\":123,\"y\":265.3999938964844},{\"taskCode\":**************,\"x\":190,\"y\":460}]', NULL, 0, '-1', 0, '1', '2023-10-30 18:06:15', '2023-10-10 16:41:24', '2023-10-30 18:06:15');
INSERT INTO `t_ds_process_definition_log` VALUES ('11508998348352', 11508998330944, 'dag01', 1, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '10967307334208', '2023-11-07 16:07:29', '2023-11-07 16:07:29', '2023-11-07 16:07:29');
INSERT INTO `t_ds_process_definition_log` VALUES ('11509105525952', 11508998330944, 'dag01', 2, '', **************, 1, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '10967307334208', '2023-11-07 16:21:27', '2023-11-07 16:07:29', '2023-11-07 16:21:27');
INSERT INTO `t_ds_process_definition_log` VALUES ('11509303671488', 11508998330944, 'dag01', 3, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '10967307334208', '2023-11-07 16:47:15', '2023-11-07 16:07:29', '2023-11-07 16:47:15');
INSERT INTO `t_ds_process_definition_log` VALUES ('11509377736384', 11508998330944, 'dag01', 4, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '10967307334208', '2023-11-07 16:56:54', '2023-11-07 16:07:29', '2023-11-07 16:56:54');
INSERT INTO `t_ds_process_definition_log` VALUES ('11509787533248', 11508998330944, 'dag01', 5, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '10967307334208', '2023-11-07 18:07:55', '2023-11-07 16:07:29', '2023-11-07 18:07:55');
INSERT INTO `t_ds_process_definition_log` VALUES ('11574571175584', 11508998330944, 'dag01', 6, '', **************, 0, '10967307334208', '[]', 1, '[{\"taskCode\":11508994849856,\"x\":31.22222900390625,\"y\":63.111114501953125}]', NULL, 0, '-1', 0, '4', '2023-11-07 18:07:55', '2023-11-07 16:07:29', '2023-11-07 18:07:55');
INSERT INTO `t_ds_process_definition_log` VALUES ('11574900117408', 11276197971872, '测试定时工作流新增', 2, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575001655584', 11276197971872, '测试定时工作流新增', 3, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575037724832', 11276197971872, '测试定时工作流新增', 4, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575064013600', 11276197971872, '测试定时工作流新增', 5, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575071245088', 11276197971872, '测试定时工作流新增', 6, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575076354592', 11276197971872, '测试定时工作流新增', 7, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575077882656', 11276197971872, '测试定时工作流新增', 8, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575120129440', 11276197971872, '测试定时工作流新增', 9, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575131515552', 11276197971872, '测试定时工作流新增', 10, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575143619744', 11276197971872, '测试定时工作流新增', 11, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575206758560', 11276197971872, '测试定时工作流新增', 12, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575212376480', 11276197971872, '测试定时工作流新增', 13, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575215129120', 11276197971872, '测试定时工作流新增', 14, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575254821408', 11276197971872, '测试定时工作流新增', 15, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_definition_log` VALUES ('11575272454176', 11276197971872, '测试定时工作流新增', 16, '测试定时工作流新增', **************, 0, '1', '[]', 1, '[{\"taskCode\":11276191674144,\"x\":101,\"y\":66}]', NULL, 0, '-1', 0, '4', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');

-- ----------------------------
-- Table structure for t_ds_process_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_process_instance`;
CREATE TABLE `t_ds_process_instance`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process instance name',
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process definition code',
  `process_definition_version` int(0) NULL DEFAULT 0 COMMENT 'process definition version',
  `state` tinyint(0) NULL DEFAULT NULL COMMENT 'process instance Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete',
  `state_history` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'state history desc',
  `recovery` tinyint(0) NULL DEFAULT NULL COMMENT 'process instance failover flag：0:normal,1:failover instance',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT 'process instance start time',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT 'process instance end time',
  `run_times` int(0) NULL DEFAULT NULL COMMENT 'process instance run times',
  `host` varchar(135) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process instance host',
  `command_type` tinyint(0) NULL DEFAULT NULL COMMENT 'command type',
  `command_param` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'json command parameters',
  `task_depend_type` tinyint(0) NULL DEFAULT NULL COMMENT 'task depend type. 0: only current node,1:before the node,2:later nodes',
  `max_try_times` tinyint(0) NULL DEFAULT 0 COMMENT 'max try times',
  `failure_strategy` tinyint(0) NULL DEFAULT 0 COMMENT 'failure strategy. 0:end the process when node failed,1:continue running the other nodes when node failed',
  `warning_type` tinyint(0) NULL DEFAULT 0 COMMENT 'warning type. 0:no warning,1:warning if process success,2:warning if process failed,3:warning if success',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'warning group id',
  `schedule_time` datetime(0) NULL DEFAULT NULL COMMENT 'schedule time',
  `command_start_time` datetime(0) NULL DEFAULT NULL COMMENT 'command start time',
  `global_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'global parameters',
  `flag` tinyint(0) NULL DEFAULT 1 COMMENT 'flag',
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `is_sub_process` int(0) NULL DEFAULT 0 COMMENT 'flag, whether the process is sub process',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'executor id',
  `history_cmd` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'history commands of process instance operation',
  `process_instance_priority` int(0) NULL DEFAULT 2 COMMENT 'process instance priority. 0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker group id',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'time out',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '-1' COMMENT 'tenant id',
  `var_pool` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'var_pool',
  `dry_run` tinyint(0) NULL DEFAULT 0 COMMENT 'dry run flag：0 normal, 1 dry run',
  `next_process_instance_id` int(0) NULL DEFAULT 0 COMMENT 'serial queue next processInstanceId',
  `restart_time` datetime(0) NULL DEFAULT NULL COMMENT 'process instance restart time',
  `test_flag` tinyint(0) NULL DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `process_instance_index`(`process_definition_code`, `id`) USING BTREE,
  INDEX `start_time_index`(`start_time`, `end_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_process_instance
-- ----------------------------
INSERT INTO `t_ds_process_instance` VALUES ('10812047232704', '请求百度-1-20230905153838954', 10811931125184, 1, 7, '[{\"time\":\"2023-09-05 15:38:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 15:38:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 15:38:41\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-05 15:38:43\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 15:38:39', '2023-09-05 15:38:43', 1, '************:5678', 9, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 15:38:38', NULL, 1, '2023-09-05 15:38:44', 0, '4', 'START_PROCESS,STOP', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-05 15:38:39', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812440500544', '自定义接口测试-1-20230905162951343', 10812439524545, 1, 6, '[{\"time\":\"2023-09-05 16:29:51\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:29:51\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:29:54\",\"state\":\"FAILURE\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:29:51', '2023-09-05 16:29:55', 1, '************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:29:51', NULL, 1, '2023-09-05 16:29:55', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 16:29:51', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812453365056', '自定义接口测试-2-20230905163131864', 10812439524545, 2, 6, '[{\"time\":\"2023-09-05 16:31:31\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:31:31\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:31:35\",\"state\":\"FAILURE\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:31:32', '2023-09-05 16:31:35', 1, '************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:31:30', NULL, 1, '2023-09-05 16:31:36', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 16:31:32', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812529819200', '自定义接口测试-3-20230905164129165', 10812439524545, 3, 7, '[{\"time\":\"2023-09-05 16:41:29\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:41:29\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:41:42\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:41:29', '2023-09-05 16:41:43', 1, '************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:41:29', NULL, 1, '2023-09-05 16:41:43', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-05 16:41:29', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812534885568', '自定义接口测试-3-20230905164208745', 10812439524545, 3, 6, '[{\"time\":\"2023-09-05 16:42:08\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:42:08\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:42:17\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-05 16:42:19\",\"state\":\"STOP\",\"desc\":\"update by workflow executor\"},{\"time\":\"2023-09-05 16:43:11\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"repeat running a process\"},{\"time\":\"2023-09-05 16:43:12\",\"state\":\"FAILURE\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:43:12', '2023-09-05 16:43:12', 2, '************:5678', 7, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:42:07', NULL, 1, '2023-09-05 16:43:13', 0, '4', 'START_PROCESS,STOP,REPEAT_RUNNING', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 16:43:12', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812547434944', '自定义接口测试-3-20230905164346787', 10812439524545, 3, 5, '[{\"time\":\"2023-09-05 16:43:46\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:43:46\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:43:53\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-05 16:43:54\",\"state\":\"STOP\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:43:47', '2023-09-05 16:43:54', 1, '************:5678', 9, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:43:46', NULL, 1, '2023-09-05 16:43:55', 0, '4', 'START_PROCESS,STOP', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 16:43:47', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812552296896', '自定义接口测试-3-20230905164424772', 10812439524545, 3, 5, '[{\"time\":\"2023-09-05 16:44:24\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 16:44:24\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 16:44:30\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-05 16:44:31\",\"state\":\"STOP\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 16:44:25', '2023-09-05 16:44:31', 1, '************:5678', 9, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 16:44:23', NULL, 1, '2023-09-05 16:44:32', 0, '4', 'START_PROCESS,STOP', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 16:44:25', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10812680911296', 'shell-1-20230905170109570', 10812679584833, 1, 5, '[{\"time\":\"2023-09-05 17:01:09\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-05 17:01:09\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-05 17:01:43\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-05 17:01:44\",\"state\":\"STOP\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-05 17:01:10', '2023-09-05 17:01:44', 1, '************:5678', 9, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-05 17:01:09', NULL, 1, '2023-09-05 17:01:45', 0, '4', 'START_PROCESS,STOP', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-05 17:01:10', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10821253097408', '测试bat-1-20230906113719755', 10821251772608, 1, 7, '[{\"time\":\"2023-09-06 11:37:19\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-06 11:37:19\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-06 11:37:23\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-06 11:37:20', '2023-09-06 11:37:23', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-06 11:37:19', NULL, 1, '2023-09-06 11:37:24', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-06 11:37:20', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10822331949760', 'shell-1-20230906135748290', 10812679584833, 1, 5, '[{\"time\":\"2023-09-06 13:57:48\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-06 13:57:48\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-06 13:58:06\",\"state\":\"READY_STOP\",\"desc\":\"stop a processby ui\"},{\"time\":\"2023-09-06 13:58:07\",\"state\":\"STOP\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-06 13:57:48', '2023-09-06 13:58:08', 1, '***********:5678', 9, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-06 13:57:48', NULL, 1, '2023-09-06 13:58:08', 0, '4', 'START_PROCESS,STOP', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-06 13:57:48', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10822358161728', '自定义接口测试-3-20230906140113090', 10812439524545, 3, 7, '[{\"time\":\"2023-09-06 14:01:13\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-06 14:01:13\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-06 14:01:27\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-06 14:01:13', '2023-09-06 14:01:28', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-06 14:01:12', NULL, 1, '2023-09-06 14:01:28', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-06 14:01:13', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10835261995200', 'datax-1-20230907180124278', 10835095030848, 1, 6, '[{\"time\":\"2023-09-07 18:01:24\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-07 18:01:24\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-07 18:02:06\",\"state\":\"FAILURE\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-07 18:01:24', '2023-09-07 18:02:07', 1, '172.26.192.1:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-07 18:01:23', NULL, 1, '2023-09-07 18:02:06', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-07 18:01:24', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10835269865536', 'datax-1-20230907180225782', 10835095030848, 1, 6, '[{\"time\":\"2023-09-07 18:02:25\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-07 18:02:25\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-07 18:02:40\",\"state\":\"FAILURE\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-07 18:02:26', '2023-09-07 18:02:41', 1, '172.26.192.1:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-07 18:02:25', NULL, 1, '2023-09-07 18:02:40', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', NULL, 0, 0, '2023-09-07 18:02:26', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10844661257536', 'shell输出DAG-1-20230908142516033', 10844574533184, 1, 7, '[{\"time\":\"2023-09-08 14:25:16\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-08 14:25:16\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-08 14:25:18\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-08 14:25:16', '2023-09-08 14:25:18', 1, '***********:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-08 14:25:00', '2023-09-08 14:25:00', NULL, 1, '2023-09-08 14:25:19', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-08 14:25:16', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877723214912', 'shell输出DAG-1-20230911141012561', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:10:12\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:10:12\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:10:16\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:10:13', '2023-09-11 14:10:17', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:10:12', '2023-09-11 14:10:12', NULL, 1, '2023-09-11 14:10:16', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:10:13', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877729436992', 'shell输出DAG-1-20230911141101185', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:11:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:11:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:11:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:11:01', '2023-09-11 14:11:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:11:00', '2023-09-11 14:11:00', NULL, 1, '2023-09-11 14:11:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:11:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877754379712', 'shell输出DAG-1-20230911141416047', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:14:16\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:14:16\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:14:19\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:14:16', '2023-09-11 14:14:20', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:14:15', '2023-09-11 14:14:15', NULL, 1, '2023-09-11 14:14:19', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:14:16', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877814399424', 'shell输出DAG-1-20230911142204958', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:22:04\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:22:04\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:22:08\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:22:05', '2023-09-11 14:22:08', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:22:04', '2023-09-11 14:22:04', NULL, 1, '2023-09-11 14:22:07', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:22:05', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877821691072', 'shell输出DAG-1-20230911142301922', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:23:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:23:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:23:23\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:23:02', '2023-09-11 14:23:23', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:23:00', '2023-09-11 14:23:00', NULL, 1, '2023-09-11 14:23:22', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:23:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877829333696', 'shell输出DAG-1-20230911142401631', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:24:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:24:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:24:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:24:02', '2023-09-11 14:24:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:24:01', '2023-09-11 14:24:01', NULL, 1, '2023-09-11 14:24:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:24:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877921646912', 'shell输出DAG-1-20230911143602811', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:36:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:36:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:36:07\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:36:03', '2023-09-11 14:36:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:36:01', '2023-09-11 14:36:01', NULL, 1, '2023-09-11 14:36:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:36:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877929134400', 'shell输出DAG-1-20230911143701322', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:37:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:37:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:37:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:37:01', '2023-09-11 14:37:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:37:01', '2023-09-11 14:37:01', NULL, 1, '2023-09-11 14:37:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:37:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877936834752', 'shell输出DAG-1-20230911143801481', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:38:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:38:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:38:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:38:01', '2023-09-11 14:38:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:38:00', '2023-09-11 14:38:00', NULL, 1, '2023-09-11 14:38:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:38:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877944780480', 'shell输出DAG-1-20230911143903560', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:39:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:39:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:39:08\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:39:04', '2023-09-11 14:39:08', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:39:00', '2023-09-11 14:39:00', NULL, 1, '2023-09-11 14:39:07', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:39:04', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877952179264', 'shell输出DAG-1-20230911144001360', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:40:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:40:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:40:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:40:01', '2023-09-11 14:40:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:40:01', '2023-09-11 14:40:01', NULL, 1, '2023-09-11 14:40:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:40:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877959840704', 'shell输出DAG-1-20230911144101214', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:41:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:41:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:41:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:41:01', '2023-09-11 14:41:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:41:00', '2023-09-11 14:41:00', NULL, 1, '2023-09-11 14:41:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:41:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877967629632', 'shell输出DAG-1-20230911144202067', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:42:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:42:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:42:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:42:02', '2023-09-11 14:42:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:42:01', '2023-09-11 14:42:01', NULL, 1, '2023-09-11 14:42:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:42:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877975210304', 'shell输出DAG-1-20230911144301293', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:43:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:43:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:43:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:43:01', '2023-09-11 14:43:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:43:00', '2023-09-11 14:43:00', NULL, 1, '2023-09-11 14:43:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:43:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877982917184', 'shell输出DAG-1-20230911144401490', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:44:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:44:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:44:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:44:01', '2023-09-11 14:44:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:44:00', '2023-09-11 14:44:00', NULL, 1, '2023-09-11 14:44:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:44:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877990581824', 'shell输出DAG-1-20230911144501384', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:45:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:45:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:45:07\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:45:01', '2023-09-11 14:45:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:45:00', '2023-09-11 14:45:00', NULL, 1, '2023-09-11 14:45:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:45:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10877998270528', 'shell输出DAG-1-20230911144601452', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:46:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:46:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:46:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:46:01', '2023-09-11 14:46:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:46:00', '2023-09-11 14:46:00', NULL, 1, '2023-09-11 14:46:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:46:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878006107072', 'shell输出DAG-1-20230911144702674', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:47:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:47:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:47:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:47:03', '2023-09-11 14:47:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:47:01', '2023-09-11 14:47:01', NULL, 1, '2023-09-11 14:47:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:47:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878013791424', 'shell输出DAG-1-20230911144802708', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:48:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:48:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:48:07\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:48:03', '2023-09-11 14:48:08', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:48:02', '2023-09-11 14:48:02', NULL, 1, '2023-09-11 14:48:07', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:48:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878021236672', 'shell输出DAG-1-20230911144900875', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:49:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:49:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:49:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:49:01', '2023-09-11 14:49:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:49:00', '2023-09-11 14:49:00', NULL, 1, '2023-09-11 14:49:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:49:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878028931520', 'shell输出DAG-1-20230911145000988', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:50:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:50:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:50:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:50:01', '2023-09-11 14:50:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:50:00', '2023-09-11 14:50:00', NULL, 1, '2023-09-11 14:50:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:50:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878036744640', 'shell输出DAG-1-20230911145102030', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:51:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:51:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:51:07\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:51:02', '2023-09-11 14:51:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:51:01', '2023-09-11 14:51:01', NULL, 1, '2023-09-11 14:51:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:51:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878044334016', 'shell输出DAG-1-20230911145201322', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:52:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:52:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:52:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:52:01', '2023-09-11 14:52:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:52:01', '2023-09-11 14:52:01', NULL, 1, '2023-09-11 14:52:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:52:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878052053312', 'shell输出DAG-1-20230911145301629', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:53:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:53:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:53:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:53:02', '2023-09-11 14:53:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:53:00', '2023-09-11 14:53:00', NULL, 1, '2023-09-11 14:53:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:53:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878059708608', 'shell输出DAG-1-20230911145401434', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:54:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:54:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:54:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:54:01', '2023-09-11 14:54:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:54:00', '2023-09-11 14:54:00', NULL, 1, '2023-09-11 14:54:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:54:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878067353152', 'shell输出DAG-1-20230911145501161', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:55:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:55:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:55:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:55:01', '2023-09-11 14:55:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:55:00', '2023-09-11 14:55:00', NULL, 1, '2023-09-11 14:55:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:55:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878075039552', 'shell输出DAG-1-20230911145601205', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:56:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:56:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:56:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:56:01', '2023-09-11 14:56:04', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:56:01', '2023-09-11 14:56:01', NULL, 1, '2023-09-11 14:56:03', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:56:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878082715072', 'shell输出DAG-1-20230911145701174', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:57:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:57:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:57:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:57:01', '2023-09-11 14:57:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:57:00', '2023-09-11 14:57:00', NULL, 1, '2023-09-11 14:57:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:57:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878090430784', 'shell输出DAG-1-20230911145801453', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:58:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:58:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:58:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:58:01', '2023-09-11 14:58:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:58:00', '2023-09-11 14:58:00', NULL, 1, '2023-09-11 14:58:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:58:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878098129344', 'shell输出DAG-1-20230911145901578', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 14:59:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 14:59:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 14:59:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 14:59:02', '2023-09-11 14:59:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 14:59:00', '2023-09-11 14:59:00', NULL, 1, '2023-09-11 14:59:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 14:59:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878105712192', 'shell输出DAG-1-20230911150000838', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:00:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:00:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:00:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:00:01', '2023-09-11 15:00:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:00:00', '2023-09-11 15:00:00', NULL, 1, '2023-09-11 15:00:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:00:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878113511616', 'shell输出DAG-1-20230911150101770', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:01:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:01:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:01:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:01:02', '2023-09-11 15:01:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:01:00', '2023-09-11 15:01:00', NULL, 1, '2023-09-11 15:01:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:01:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878121172288', 'shell输出DAG-1-20230911150201622', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:02:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:02:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:02:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:02:02', '2023-09-11 15:02:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:02:00', '2023-09-11 15:02:00', NULL, 1, '2023-09-11 15:02:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:02:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878128782272', 'shell输出DAG-1-20230911150301073', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:03:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:03:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:03:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:03:01', '2023-09-11 15:03:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:03:00', '2023-09-11 15:03:00', NULL, 1, '2023-09-11 15:03:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:03:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878136589120', 'shell输出DAG-1-20230911150402065', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:04:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:04:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:04:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:04:02', '2023-09-11 15:04:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:04:00', '2023-09-11 15:04:00', NULL, 1, '2023-09-11 15:04:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:04:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878144243776', 'shell输出DAG-1-20230911150501867', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:05:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:05:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:05:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:05:02', '2023-09-11 15:05:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:05:00', '2023-09-11 15:05:00', NULL, 1, '2023-09-11 15:05:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:05:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878151946560', 'shell输出DAG-1-20230911150602044', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:06:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:06:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:06:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:06:02', '2023-09-11 15:06:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:06:00', '2023-09-11 15:06:00', NULL, 1, '2023-09-11 15:06:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:06:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878159649600', 'shell输出DAG-1-20230911150702225', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:07:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:07:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:07:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:07:02', '2023-09-11 15:07:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:07:00', '2023-09-11 15:07:00', NULL, 1, '2023-09-11 15:07:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:07:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878167443008', 'shell输出DAG-1-20230911150803111', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:08:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:08:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:08:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:08:03', '2023-09-11 15:08:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:08:01', '2023-09-11 15:08:01', NULL, 1, '2023-09-11 15:08:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:08:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878174866752', 'shell输出DAG-1-20230911150901109', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:09:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:09:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:09:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:09:01', '2023-09-11 15:09:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:09:00', '2023-09-11 15:09:00', NULL, 1, '2023-09-11 15:09:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:09:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878182537536', 'shell输出DAG-1-20230911151001036', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:10:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:10:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:10:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:10:01', '2023-09-11 15:10:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:10:00', '2023-09-11 15:10:00', NULL, 1, '2023-09-11 15:10:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:10:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878190200768', 'shell输出DAG-1-20230911151100906', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:11:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:11:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:11:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:11:01', '2023-09-11 15:11:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:11:00', '2023-09-11 15:11:00', NULL, 1, '2023-09-11 15:11:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:11:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878197928128', 'shell输出DAG-1-20230911151201272', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 15:12:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 15:12:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 15:12:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 15:12:01', '2023-09-11 15:12:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 15:12:00', '2023-09-11 15:12:00', NULL, 1, '2023-09-11 15:12:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 15:12:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878858574912', 'shell输出DAG-1-20230911163802548', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:38:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:38:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:38:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:38:03', '2023-09-11 16:38:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:38:01', '2023-09-11 16:38:01', NULL, 1, '2023-09-11 16:38:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:38:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878866197312', 'shell输出DAG-1-20230911163902126', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:39:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:39:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:39:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:39:02', '2023-09-11 16:39:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:39:01', '2023-09-11 16:39:01', NULL, 1, '2023-09-11 16:39:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:39:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878873790016', 'shell输出DAG-1-20230911164001441', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:40:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:40:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:40:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:40:01', '2023-09-11 16:40:04', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:40:01', '2023-09-11 16:40:01', NULL, 1, '2023-09-11 16:40:03', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:40:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878881786688', 'shell输出DAG-1-20230911164103918', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:41:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:41:03\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:41:08\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:41:04', '2023-09-11 16:41:08', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:41:02', '2023-09-11 16:41:02', NULL, 1, '2023-09-11 16:41:07', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:41:04', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878889151680', 'shell输出DAG-1-20230911164201459', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:42:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:42:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:42:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:42:01', '2023-09-11 16:42:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:42:00', '2023-09-11 16:42:00', NULL, 1, '2023-09-11 16:42:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:42:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878896780608', 'shell输出DAG-1-20230911164301058', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:43:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:43:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:43:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:43:01', '2023-09-11 16:43:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:43:00', '2023-09-11 16:43:00', NULL, 1, '2023-09-11 16:43:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:43:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878904538432', 'shell输出DAG-1-20230911164401668', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:44:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:44:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:44:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:44:02', '2023-09-11 16:44:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:44:01', '2023-09-11 16:44:01', NULL, 1, '2023-09-11 16:44:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:44:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878912113728', 'shell输出DAG-1-20230911164500850', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:45:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:45:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:45:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:45:01', '2023-09-11 16:45:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:45:00', '2023-09-11 16:45:00', NULL, 1, '2023-09-11 16:45:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:45:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878919795520', 'shell输出DAG-1-20230911164600865', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:46:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:46:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:46:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:46:01', '2023-09-11 16:46:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:46:00', '2023-09-11 16:46:00', NULL, 1, '2023-09-11 16:46:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:46:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878927478336', 'shell输出DAG-1-20230911164700886', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:47:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:47:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:47:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:47:01', '2023-09-11 16:47:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:47:00', '2023-09-11 16:47:00', NULL, 1, '2023-09-11 16:47:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:47:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878935318848', 'shell输出DAG-1-20230911164802140', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:48:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:48:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:48:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:48:02', '2023-09-11 16:48:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:48:01', '2023-09-11 16:48:01', NULL, 1, '2023-09-11 16:48:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:48:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878942971840', 'shell输出DAG-1-20230911164901929', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:49:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:49:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:49:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:49:02', '2023-09-11 16:49:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:49:01', '2023-09-11 16:49:01', NULL, 1, '2023-09-11 16:49:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:49:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878950781632', 'shell输出DAG-1-20230911165002941', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:50:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:50:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:50:08\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:50:03', '2023-09-11 16:50:09', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:50:01', '2023-09-11 16:50:01', NULL, 1, '2023-09-11 16:50:08', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:50:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878958251200', 'shell输出DAG-1-20230911165101299', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:51:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:51:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:51:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:51:01', '2023-09-11 16:51:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:51:01', '2023-09-11 16:51:01', NULL, 1, '2023-09-11 16:51:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:51:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878966008640', 'shell输出DAG-1-20230911165201903', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:52:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:52:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:52:09\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:52:02', '2023-09-11 16:52:10', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:52:01', '2023-09-11 16:52:01', NULL, 1, '2023-09-11 16:52:09', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:52:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878973686592', 'shell输出DAG-1-20230911165301888', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:53:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:53:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:53:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:53:02', '2023-09-11 16:53:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:53:01', '2023-09-11 16:53:01', NULL, 1, '2023-09-11 16:53:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:53:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878981380544', 'shell输出DAG-1-20230911165401997', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:54:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:54:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:54:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:54:02', '2023-09-11 16:54:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:54:00', '2023-09-11 16:54:00', NULL, 1, '2023-09-11 16:54:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:54:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878988994112', 'shell输出DAG-1-20230911165501478', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:55:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:55:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:55:10\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:55:01', '2023-09-11 16:55:10', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:55:01', '2023-09-11 16:55:01', NULL, 1, '2023-09-11 16:55:09', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:55:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10878996766144', 'shell输出DAG-1-20230911165602197', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:56:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:56:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:56:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:56:02', '2023-09-11 16:56:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:56:01', '2023-09-11 16:56:01', NULL, 1, '2023-09-11 16:56:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:56:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879004509120', 'shell输出DAG-1-20230911165702690', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:57:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:57:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:57:07\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:57:03', '2023-09-11 16:57:08', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:57:00', '2023-09-11 16:57:00', NULL, 1, '2023-09-11 16:57:07', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:57:03', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879012032448', 'shell输出DAG-1-20230911165801464', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:58:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:58:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:58:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:58:01', '2023-09-11 16:58:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:58:01', '2023-09-11 16:58:01', NULL, 1, '2023-09-11 16:58:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:58:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879019667904', 'shell输出DAG-1-20230911165901118', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 16:59:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 16:59:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 16:59:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 16:59:01', '2023-09-11 16:59:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 16:59:01', '2023-09-11 16:59:01', NULL, 1, '2023-09-11 16:59:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 16:59:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879027383232', 'shell输出DAG-1-20230911170001394', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:00:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:00:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:00:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:00:01', '2023-09-11 17:00:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:00:00', '2023-09-11 17:00:00', NULL, 1, '2023-09-11 17:00:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:00:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879035007680', 'shell输出DAG-1-20230911170100957', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:01:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:01:00\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:01:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:01:01', '2023-09-11 17:01:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:01:00', '2023-09-11 17:01:00', NULL, 1, '2023-09-11 17:01:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:01:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879042850368', 'shell输出DAG-1-20230911170202229', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:02:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:02:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:02:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:02:02', '2023-09-11 17:02:07', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:02:01', '2023-09-11 17:02:01', NULL, 1, '2023-09-11 17:02:06', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:02:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879050448320', 'shell输出DAG-1-20230911170301589', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:03:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:03:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:03:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:03:02', '2023-09-11 17:03:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:03:00', '2023-09-11 17:03:00', NULL, 1, '2023-09-11 17:03:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:03:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879058077376', 'shell输出DAG-1-20230911170401192', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:04:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:04:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:04:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:04:01', '2023-09-11 17:04:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:04:00', '2023-09-11 17:04:00', NULL, 1, '2023-09-11 17:04:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:04:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879065761216', 'shell输出DAG-1-20230911170501220', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:05:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:05:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:05:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:05:01', '2023-09-11 17:05:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:05:01', '2023-09-11 17:05:01', NULL, 1, '2023-09-11 17:05:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:05:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879073541312', 'shell输出DAG-1-20230911170602005', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:06:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:06:02\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:06:06\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:06:02', '2023-09-11 17:06:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:06:01', '2023-09-11 17:06:01', NULL, 1, '2023-09-11 17:06:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:06:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879081215168', 'shell输出DAG-1-20230911170701956', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:07:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:07:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:07:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:07:02', '2023-09-11 17:07:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:07:00', '2023-09-11 17:07:00', NULL, 1, '2023-09-11 17:07:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:07:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879088863808', 'shell输出DAG-1-20230911170801712', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:08:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:08:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:08:05\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:08:02', '2023-09-11 17:08:06', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:08:00', '2023-09-11 17:08:00', NULL, 1, '2023-09-11 17:08:05', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:08:02', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10879096501056', 'shell输出DAG-1-20230911170901376', 10844574533184, 1, 7, '[{\"time\":\"2023-09-11 17:09:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-11 17:09:01\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process from scheduler\"},{\"time\":\"2023-09-11 17:09:04\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-11 17:09:01', '2023-09-11 17:09:05', 1, '************:5678', 6, '{\"schedule_timezone\":\"Asia/Shanghai\"}', 2, 0, 1, 0, '0', '2023-09-11 17:09:00', '2023-09-11 17:09:00', NULL, 1, '2023-09-11 17:09:04', 0, '4', 'SCHEDULER', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-11 17:09:01', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10989977898304', '测试001-3-20230921174642262', 10989552944256, 3, 7, '[{\"time\":\"2023-09-21 17:46:42\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-21 17:46:42\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-21 17:46:50\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-21 17:46:42', '2023-09-21 17:46:50', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-21 17:46:41', NULL, 1, '2023-09-21 17:46:49', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-21 17:46:42', 1, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10990000279872', '测试001-4-20230921174937145', 10989552944256, 4, 7, '[{\"time\":\"2023-09-21 17:49:37\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-21 17:49:37\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-21 17:49:42\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-21 17:49:37', '2023-09-21 17:49:42', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-21 17:49:36', NULL, 1, '2023-09-21 17:49:42', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-21 17:49:37', 1, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10990031629760', '测试001-5-20230921175342066', 10989552944256, 5, 7, '[{\"time\":\"2023-09-21 17:53:42\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-21 17:53:42\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-21 17:53:47\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-21 17:53:42', '2023-09-21 17:53:47', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-21 17:53:41', NULL, 1, '2023-09-21 17:53:46', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-21 17:53:42', 1, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10990046844352', '测试001-6-20230921175540931', 10989552944256, 6, 7, '[{\"time\":\"2023-09-21 17:55:40\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-21 17:55:40\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-21 17:55:49\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-21 17:55:41', '2023-09-21 17:55:49', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-21 17:55:40', NULL, 1, '2023-09-21 17:55:48', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-21 17:55:41', 1, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('10990092650048', '测试001-7-20230921180138788', 10989552944256, 7, 7, '[{\"time\":\"2023-09-21 18:01:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-09-21 18:01:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-09-21 18:01:47\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-09-21 18:01:39', '2023-09-21 18:01:47', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-09-21 18:01:38', NULL, 1, '2023-09-21 18:01:47', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-09-21 18:01:39', 1, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('**************', 'st_linux-1-********161338784', 11199358291360, 1, 7, '[{\"time\":\"2023-10-10 16:13:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-10-10 16:13:38\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-10-10 16:14:20\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-10-10 16:13:39', '2023-10-10 16:14:20', 1, '**************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-10-10 16:13:38', NULL, 1, '2023-10-10 16:14:20', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-10-10 16:13:39', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('**************', 'st_linux-1-********163119094', 11199358291360, 1, 7, '[{\"time\":\"2023-10-10 16:31:19\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-10-10 16:31:19\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-10-10 16:32:17\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-10-10 16:31:19', '2023-10-10 16:32:17', 1, '**************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-10-10 16:31:18', NULL, 1, '2023-10-10 16:32:17', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-10-10 16:31:19', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('**************', 'st_cluster-1-********164158309', 11199601127968, 2, 7, '[{\"time\":\"2023-10-10 16:41:58\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-10-10 16:41:58\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-10-10 16:42:38\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-10-10 16:41:58', '2023-10-10 16:42:38', 1, '**************:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-10-10 16:41:57', NULL, 1, '2023-10-30 18:06:15', 0, '4', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-10-10 16:41:58', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('11509093038656', 'dag01-1-20231107161949302', 11508998330944, 2, 7, '[{\"time\":\"2023-11-07 16:19:49\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-11-07 16:19:49\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-11-07 16:19:52\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"},{\"time\":\"2023-11-07 16:21:33\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"repeat running a process\"},{\"time\":\"2023-11-07 16:21:34\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-11-07 16:21:34', '2023-11-07 16:21:34', 2, '***********:5678', 7, '{}', 2, 0, 1, 0, '0', NULL, '2023-11-07 16:19:48', NULL, 1, '2023-11-07 16:21:33', 0, '10967307334208', 'START_PROCESS,REPEAT_RUNNING', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-11-07 16:21:34', 0, NULL);
INSERT INTO `t_ds_process_instance` VALUES ('11509910789824', 'dag01-5-20231107180617983', 11508998330944, 5, 7, '[{\"time\":\"2023-11-07 18:06:17\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"init running\"},{\"time\":\"2023-11-07 18:06:17\",\"state\":\"RUNNING_EXECUTION\",\"desc\":\"start a new process\"},{\"time\":\"2023-11-07 18:06:20\",\"state\":\"SUCCESS\",\"desc\":\"update by workflow executor\"}]', 0, '2023-11-07 18:06:18', '2023-11-07 18:06:21', 1, '***********:5678', 0, '{}', 2, 0, 1, 0, '0', NULL, '2023-11-07 18:06:17', NULL, 1, '2023-11-07 18:06:20', 0, '10967307334208', 'START_PROCESS', 2, 'default', -1, 0, '-1', '[]', 0, 0, '2023-11-07 18:06:18', 0, NULL);

-- ----------------------------
-- Table structure for t_ds_process_task_relation
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_process_task_relation`;
CREATE TABLE `t_ds_process_task_relation`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'relation name',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process code',
  `process_definition_version` int(0) NOT NULL COMMENT 'process version',
  `pre_task_code` bigint(0) NOT NULL COMMENT 'pre task code',
  `pre_task_version` int(0) NOT NULL COMMENT 'pre task version',
  `post_task_code` bigint(0) NOT NULL COMMENT 'post task code',
  `post_task_version` int(0) NOT NULL COMMENT 'post task version',
  `condition_type` tinyint(0) NULL DEFAULT NULL COMMENT 'condition type : 0 none, 1 judge 2 delay',
  `condition_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'condition params(json)',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_code`(`project_code`, `process_definition_code`) USING BTREE,
  INDEX `idx_pre_task_code_version`(`pre_task_code`, `pre_task_version`) USING BTREE,
  INDEX `idx_post_task_code_version`(`post_task_code`, `post_task_version`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_process_task_relation
-- ----------------------------
INSERT INTO `t_ds_process_task_relation` VALUES ('10812528328640', '', **************, 10812439524545, 3, 0, 0, 10812436419776, 3, 0, '{}', '2023-09-05 16:41:18', '2023-09-05 16:41:18');
INSERT INTO `t_ds_process_task_relation` VALUES ('10812679615040', '', **************, 10812679584833, 1, 0, 0, 10812663635136, 1, 0, '{}', '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_process_task_relation` VALUES ('10821251804224', '', **************, 10821251772608, 1, 0, 0, 10821244252096, 1, 0, '{}', '2023-09-06 11:37:10', '2023-09-06 11:37:10');
INSERT INTO `t_ds_process_task_relation` VALUES ('10835095062848', '', **************, 10835095030848, 1, 0, 0, 10835090078656, 1, 0, '{}', '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_process_task_relation` VALUES ('10844574559808', '', **************, 10844574533184, 1, 0, 0, 10844570462400, 1, 0, '{}', '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_process_task_relation` VALUES ('10953574493760', '', **************, 10811931125184, 1, 0, 0, 10811878809280, 1, 0, '{}', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation` VALUES ('10953574493761', NULL, **************, 10811931125184, 1, 0, 0, 10953569347136, 1, 0, '{}', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation` VALUES ('10953574493762', NULL, **************, 10811931125184, 1, 0, 0, 10953574443072, 1, 0, '{}', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation` VALUES ('11178114522016', '', **************, 11178114471456, 1, 0, 0, 11177851623200, 3, 0, '{}', '2023-10-08 18:03:40', '2023-10-08 18:03:40');
INSERT INTO `t_ds_process_task_relation` VALUES ('11196578151104', '', **************, 11196578111424, 1, 0, 0, **************, 1, 0, '{}', '2023-10-10 10:07:47', '2023-10-10 10:07:47');
INSERT INTO `t_ds_process_task_relation` VALUES ('11198799552192', '', **************, 11198719105088, 4, 0, 0, **************, 4, 0, '{}', '2023-10-10 14:57:02', '2023-10-10 14:57:02');
INSERT INTO `t_ds_process_task_relation` VALUES ('11199358294304', '', **************, 11199358291360, 1, 0, 0, **************, 1, 0, '{}', '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_process_task_relation` VALUES ('11199601130272', '', **************, 11199601127968, 1, 0, 0, **************, 1, 0, '{}', '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_process_task_relation` VALUES ('11199702783296', '', 11199696680000, 11199702752064, 1, 0, 0, 11199699252928, 1, 0, '{}', '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_process_task_relation` VALUES ('11199720147904', '', 11199696680000, 11199720118592, 1, 0, 0, 11199717663680, 1, 0, '{}', '2023-10-10 16:56:54', '2023-10-10 16:56:54');
INSERT INTO `t_ds_process_task_relation` VALUES ('11421308169536', '', 11199696680000, 11421193780800, 6, 0, 0, 11421174732096, 2, 0, '{}', '2023-10-30 17:49:30', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_task_relation` VALUES ('11421308169537', '', 11199696680000, 11421193780800, 6, 11421174732096, 2, 11421191059392, 2, 0, '{}', '2023-10-30 17:49:30', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_task_relation` VALUES ('11421317278272', '', 11199696680000, 11421317248066, 1, 0, 0, 11421313640256, 1, 0, '{}', '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_task_relation` VALUES ('11421317278273', '', 11199696680000, 11421317248066, 1, 11421313640256, 1, 11421314732992, 1, 0, '{}', '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_task_relation` VALUES ('11574571196320', '', **************, 11508998330944, 6, 0, 0, 11508994849856, 6, 0, '{}', '2023-11-13 14:25:37', '2023-11-13 14:25:37');
INSERT INTO `t_ds_process_task_relation` VALUES ('11575272535200', '', **************, 11276197971872, 16, 0, 0, 11276191674144, 1, 0, '{}', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation` VALUES ('11575272535201', NULL, **************, 11276197971872, 16, 11276191674144, 1, 11574566356256, 1, 0, '{}', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation` VALUES ('11575272535202', NULL, **************, 11276197971872, 16, 11574566356256, 1, 11575146272416, 1, 0, '{}', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation` VALUES ('11575272535203', NULL, **************, 11276197971872, 16, 0, 0, 11574629217831, 4, 0, '{}', '2023-11-13 15:56:57', '2023-11-13 15:56:57');

-- ----------------------------
-- Table structure for t_ds_process_task_relation_log
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_process_task_relation_log`;
CREATE TABLE `t_ds_process_task_relation_log`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'relation name',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process code',
  `process_definition_version` int(0) NOT NULL COMMENT 'process version',
  `pre_task_code` bigint(0) NOT NULL COMMENT 'pre task code',
  `pre_task_version` int(0) NOT NULL COMMENT 'pre task version',
  `post_task_code` bigint(0) NOT NULL COMMENT 'post task code',
  `post_task_version` int(0) NOT NULL COMMENT 'post task version',
  `condition_type` tinyint(0) NULL DEFAULT NULL COMMENT 'condition type : 0 none, 1 judge 2 delay',
  `condition_params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'condition params(json)',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `operate_time` datetime(0) NULL DEFAULT NULL COMMENT 'operate time',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_process_code_version`(`process_definition_code`, `process_definition_version`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_process_task_relation_log
-- ----------------------------
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10811931157824', '', **************, 10811931125184, 1, 0, 0, 10811878809280, 1, 0, '{}', '4', '2023-09-05 15:23:32', '2023-09-05 15:23:32', '2023-09-05 15:23:32');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10812427284544', '', **************, 10812427252288, 1, 0, 0, 10812422708672, 1, 0, '{}', '1', '2023-09-05 16:28:08', '2023-09-05 16:28:08', '2023-09-05 16:28:08');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10812439555008', '', **************, 10812439524545, 1, 0, 0, 10812436419776, 1, 0, '{}', '4', '2023-09-05 16:29:44', '2023-09-05 16:29:44', '2023-09-05 16:29:44');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10812452107328', '', **************, 10812439524545, 2, 0, 0, 10812436419776, 2, 0, '{}', '4', '2023-09-05 16:31:22', '2023-09-05 16:31:22', '2023-09-05 16:31:22');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10812528328640', '', **************, 10812439524545, 3, 0, 0, 10812436419776, 3, 0, '{}', '4', '2023-09-05 16:41:18', '2023-09-05 16:41:18', '2023-09-05 16:41:18');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10812679615040', '', **************, 10812679584833, 1, 0, 0, 10812663635136, 1, 0, '{}', '4', '2023-09-05 17:00:59', '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10821251804224', '', **************, 10821251772608, 1, 0, 0, 10821244252096, 1, 0, '{}', '4', '2023-09-06 11:37:10', '2023-09-06 11:37:10', '2023-09-06 11:37:10');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10835095062848', '', **************, 10835095030848, 1, 0, 0, 10835090078656, 1, 0, '{}', '4', '2023-09-07 17:39:40', '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10844574559808', '', **************, 10844574533184, 1, 0, 0, 10844570462400, 1, 0, '{}', '4', '2023-09-08 14:13:59', '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10953569415232', '', **************, 10811931125184, 1, 0, 0, 10811878809280, 1, 0, '{}', '4', '2023-09-18 10:46:01', '2023-09-18 10:46:01', '2023-09-18 10:46:01');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10953569415233', NULL, **************, 10811931125184, 1, 0, 0, 10953569347136, 1, 0, '{}', '4', '2023-09-18 10:46:01', '2023-09-18 10:46:01', '2023-09-18 10:46:01');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10953574493760', '', **************, 10811931125184, 1, 0, 0, 10811878809280, 1, 0, '{}', '4', '2023-09-18 10:46:41', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10953574493761', NULL, **************, 10811931125184, 1, 0, 0, 10953569347136, 1, 0, '{}', '4', '2023-09-18 10:46:41', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10953574493762', NULL, **************, 10811931125184, 1, 0, 0, 10953574443072, 1, 0, '{}', '4', '2023-09-18 10:46:41', '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10989190424384', '', **************, 10989190396736, 1, 0, 0, 10989164532800, 1, 0, '{}', '1', '2023-09-21 16:04:10', '2023-09-21 16:04:10', '2023-09-21 16:04:10');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10989698853568', '', **************, 10989552944256, 1, 0, 0, 10989164532800, 2, 0, '{}', '1', '2023-09-21 17:10:22', '2023-09-21 17:10:22', '2023-09-21 17:10:22');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10989785318720', '', **************, 10989552944256, 2, 0, 0, 10989164532800, 3, 0, '{}', '1', '2023-09-21 17:21:38', '2023-09-21 17:21:38', '2023-09-21 17:21:38');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10989976747072', '', **************, 10989552944256, 3, 0, 0, 10989164532800, 4, 0, '{}', '4', '2023-09-21 17:46:33', '2023-09-21 17:46:33', '2023-09-21 17:46:33');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10989999836864', '', **************, 10989552944256, 4, 0, 0, 10989164532800, 5, 0, '{}', '4', '2023-09-21 17:49:34', '2023-09-21 17:49:34', '2023-09-21 17:49:34');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10990031194560', '', **************, 10989552944256, 5, 0, 0, 10989164532800, 6, 0, '{}', '4', '2023-09-21 17:53:39', '2023-09-21 17:53:39', '2023-09-21 17:53:39');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10990046063040', '', **************, 10989552944256, 6, 0, 0, 10989164532800, 7, 0, '{}', '4', '2023-09-21 17:55:35', '2023-09-21 17:55:35', '2023-09-21 17:55:35');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('10990092239808', '', **************, 10989552944256, 7, 0, 0, 10989164532800, 8, 0, '{}', '4', '2023-09-21 18:01:36', '2023-09-21 18:01:36', '2023-09-21 18:01:36');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11177874938144', '', **************, 11177864412192, 1, 0, 0, 11177851623200, 1, 0, '{}', '1', '2023-10-08 17:32:28', '2023-10-08 17:32:28', '2023-10-08 17:32:28');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11177928832160', '', **************, 11177919249312, 1, 0, 0, 11177851623200, 2, 0, '{}', '1', '2023-10-08 17:39:29', '2023-10-08 17:39:29', '2023-10-08 17:39:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11178114522016', '', **************, 11178114471456, 1, 0, 0, 11177851623200, 3, 0, '{}', '1', '2023-10-08 18:03:40', '2023-10-08 18:03:40', '2023-10-08 18:03:40');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11196578151104', '', **************, 11196578111424, 1, 0, 0, **************, 1, 0, '{}', '4', '2023-10-10 10:07:47', '2023-10-10 10:07:47', '2023-10-10 10:07:47');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11198719137216', '', **************, 11198719105088, 1, 0, 0, **************, 1, 0, '{}', '4', '2023-10-10 14:46:33', '2023-10-10 14:46:33', '2023-10-10 14:46:33');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11198794720192', '', **************, 11198719105088, 2, 0, 0, **************, 2, 0, '{}', '4', '2023-10-10 14:56:24', '2023-10-10 14:56:24', '2023-10-10 14:56:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11198795645504', '', **************, 11198719105088, 3, 0, 0, **************, 3, 0, '{}', '4', '2023-10-10 14:56:31', '2023-10-10 14:56:31', '2023-10-10 14:56:31');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11198799552192', '', **************, 11198719105088, 4, 0, 0, **************, 4, 0, '{}', '4', '2023-10-10 14:57:02', '2023-10-10 14:57:02', '2023-10-10 14:57:02');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11199358294304', '', **************, 11199358291360, 1, 0, 0, **************, 1, 0, '{}', '4', '2023-10-10 16:09:47', '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11199601130272', '', **************, 11199601127968, 1, 0, 0, **************, 1, 0, '{}', '4', '2023-10-10 16:41:24', '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11199702783296', '', 11199696680000, 11199702752064, 1, 0, 0, 11199699252928, 1, 0, '{}', '4', '2023-10-10 16:54:38', '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11199720147904', '', 11199696680000, 11199720118592, 1, 0, 0, 11199717663680, 1, 0, '{}', '4', '2023-10-10 16:56:54', '2023-10-10 16:56:54', '2023-10-10 16:56:54');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11276197999904', '', **************, 11276197971872, 1, 0, 0, 11276191674144, 1, 0, '{}', '1', '2023-10-17 14:54:57', '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342480072608', '', **************, 10989552944256, 8, 0, 0, 10989164532800, 9, 0, '{}', '4', '2023-10-23 14:45:26', '2023-10-23 14:45:26', '2023-10-23 14:45:26');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342495420192', '', **************, 10989552944256, 9, 0, 0, 10989164532800, 10, 0, '{}', '4', '2023-10-23 14:47:25', '2023-10-23 14:47:25', '2023-10-23 14:47:25');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342547059488', '', **************, 10989552944256, 10, 0, 0, 10989164532800, 11, 0, '{}', '4', '2023-10-23 14:54:09', '2023-10-23 14:54:09', '2023-10-23 14:54:09');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342565324064', '', **************, 10989552944256, 11, 0, 0, 10989164532800, 12, 0, '{}', '4', '2023-10-23 14:56:32', '2023-10-23 14:56:32', '2023-10-23 14:56:32');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342579056416', '', **************, 10989552944256, 12, 0, 0, 10989164532800, 13, 0, '{}', '4', '2023-10-23 14:58:19', '2023-10-23 14:58:19', '2023-10-23 14:58:19');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342599058208', '', **************, 10989552944256, 13, 0, 0, 10989164532800, 14, 0, '{}', '4', '2023-10-23 15:00:55', '2023-10-23 15:00:55', '2023-10-23 15:00:55');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342622223136', '', **************, 10989552944256, 14, 0, 0, 10989164532800, 15, 0, '{}', '4', '2023-10-23 15:03:56', '2023-10-23 15:03:56', '2023-10-23 15:03:56');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342635666080', '', **************, 10989552944256, 15, 0, 0, 10989164532800, 16, 0, '{}', '4', '2023-10-23 15:05:41', '2023-10-23 15:05:41', '2023-10-23 15:05:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342643977888', '', **************, 10989552944256, 16, 0, 0, 10989164532800, 17, 0, '{}', '4', '2023-10-23 15:06:46', '2023-10-23 15:06:46', '2023-10-23 15:06:46');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342658895392', '', **************, 10989552944256, 17, 0, 0, 10989164532800, 18, 0, '{}', '4', '2023-10-23 15:08:43', '2023-10-23 15:08:43', '2023-10-23 15:08:43');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342871752992', '', **************, 10989552944256, 18, 0, 0, 10989164532800, 19, 0, '{}', '4', '2023-10-23 15:36:26', '2023-10-23 15:36:26', '2023-10-23 15:36:26');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342895503648', '', **************, 10989552944256, 19, 0, 0, 10989164532800, 20, 0, '{}', '4', '2023-10-23 15:39:31', '2023-10-23 15:39:31', '2023-10-23 15:39:31');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342919852192', '', **************, 10989552944256, 20, 0, 0, 10989164532800, 21, 0, '{}', '4', '2023-10-23 15:42:41', '2023-10-23 15:42:41', '2023-10-23 15:42:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11342944441632', '', **************, 10989552944256, 21, 0, 0, 10989164532800, 22, 0, '{}', '4', '2023-10-23 15:45:53', '2023-10-23 15:45:53', '2023-10-23 15:45:53');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11343030141216', '', **************, 10989552944256, 22, 0, 0, 10989164532800, 23, 0, '{}', '4', '2023-10-23 15:57:03', '2023-10-23 15:57:03', '2023-10-23 15:57:03');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11343060981408', '', **************, 10989552944256, 23, 0, 0, 10989164532800, 24, 0, '{}', '4', '2023-10-23 16:01:04', '2023-10-23 16:01:04', '2023-10-23 16:01:04');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11343074594080', '', **************, 10989552944256, 24, 0, 0, 10989164532800, 25, 0, '{}', '4', '2023-10-23 16:02:50', '2023-10-23 16:02:50', '2023-10-23 16:02:50');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421193812928', '', 11199696680000, 11421193780800, 1, 0, 0, 11421174732096, 1, 0, '{}', '1', '2023-10-30 17:34:37', '2023-10-30 17:34:37', '2023-10-30 17:34:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421193812929', '', 11199696680000, 11421193780800, 1, 11421174732096, 1, 11421191059392, 1, 0, '{}', '1', '2023-10-30 17:34:37', '2023-10-30 17:34:37', '2023-10-30 17:34:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421217376192', '', 11199696680000, 11421193780800, 2, 0, 0, 11421174732096, 1, 0, '{}', '1', '2023-10-30 17:37:41', '2023-10-30 17:37:41', '2023-10-30 17:37:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421217376320', '', 11199696680000, 11421193780800, 2, 0, 0, 11421196238912, 1, 0, '{}', '1', '2023-10-30 17:37:41', '2023-10-30 17:37:41', '2023-10-30 17:37:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421217376321', '', 11199696680000, 11421193780800, 2, 11421174732096, 1, 11421191059392, 1, 0, '{}', '1', '2023-10-30 17:37:41', '2023-10-30 17:37:41', '2023-10-30 17:37:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421223265856', '', 11199696680000, 11421193780800, 3, 0, 0, 11421174732096, 1, 0, '{}', '1', '2023-10-30 17:38:27', '2023-10-30 17:38:27', '2023-10-30 17:38:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421223265857', '', 11199696680000, 11421193780800, 3, 0, 0, 11421191059392, 1, 0, '{}', '1', '2023-10-30 17:38:27', '2023-10-30 17:38:27', '2023-10-30 17:38:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421223265858', '', 11199696680000, 11421193780800, 3, 0, 0, 11421196238912, 1, 0, '{}', '1', '2023-10-30 17:38:27', '2023-10-30 17:38:27', '2023-10-30 17:38:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421229671488', '', 11199696680000, 11421193780800, 4, 0, 0, 11421174732096, 1, 0, '{}', '1', '2023-10-30 17:39:17', '2023-10-30 17:39:17', '2023-10-30 17:39:17');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421229671489', '', 11199696680000, 11421193780800, 4, 0, 0, 11421196238912, 1, 0, '{}', '1', '2023-10-30 17:39:17', '2023-10-30 17:39:17', '2023-10-30 17:39:17');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421229671490', '', 11199696680000, 11421193780800, 4, 0, 0, 11421228664000, 1, 0, '{}', '1', '2023-10-30 17:39:17', '2023-10-30 17:39:17', '2023-10-30 17:39:17');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421229671491', '', 11199696680000, 11421193780800, 4, 11421174732096, 1, 11421191059392, 1, 0, '{}', '1', '2023-10-30 17:39:17', '2023-10-30 17:39:17', '2023-10-30 17:39:17');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421230925504', '', 11199696680000, 11421193780800, 5, 0, 0, 11421174732096, 1, 0, '{}', '1', '2023-10-30 17:39:27', '2023-10-30 17:39:27', '2023-10-30 17:39:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421230925505', '', 11199696680000, 11421193780800, 5, 0, 0, 11421196238912, 1, 0, '{}', '1', '2023-10-30 17:39:27', '2023-10-30 17:39:27', '2023-10-30 17:39:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421230925506', '', 11199696680000, 11421193780800, 5, 0, 0, 11421228664000, 1, 0, '{}', '1', '2023-10-30 17:39:27', '2023-10-30 17:39:27', '2023-10-30 17:39:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421230925507', '', 11199696680000, 11421193780800, 5, 11421174732096, 1, 11421191059392, 1, 0, '{}', '1', '2023-10-30 17:39:27', '2023-10-30 17:39:27', '2023-10-30 17:39:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421308169536', '', 11199696680000, 11421193780800, 6, 0, 0, 11421174732096, 2, 0, '{}', '1', '2023-10-30 17:49:30', '2023-10-30 17:49:30', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421308169537', '', 11199696680000, 11421193780800, 6, 11421174732096, 2, 11421191059392, 2, 0, '{}', '1', '2023-10-30 17:49:30', '2023-10-30 17:49:30', '2023-10-30 17:49:30');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421317278272', '', 11199696680000, 11421317248066, 1, 0, 0, 11421313640256, 1, 0, '{}', '1', '2023-10-30 17:50:41', '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421317278273', '', 11199696680000, 11421317248066, 1, 11421313640256, 1, 11421314732992, 1, 0, '{}', '1', '2023-10-30 17:50:41', '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421436828096', '', **************, 11199601127968, 2, 0, 0, **************, 1, 0, '{}', '1', '2023-10-30 18:06:15', '2023-10-30 18:06:15', '2023-10-30 18:06:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11421436828097', '', **************, 11199601127968, 2, 0, 0, **************, 1, 0, '{}', '1', '2023-10-30 18:06:15', '2023-10-30 18:06:15', '2023-10-30 18:06:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11508998358848', '', **************, 11508998330944, 1, 0, 0, 11508994849856, 1, 0, '{}', '10967307334208', '2023-11-07 16:07:30', '2023-11-07 16:07:30', '2023-11-07 16:07:30');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509105531456', '', **************, 11508998330944, 2, 0, 0, 11508994849856, 2, 0, '{}', '10967307334208', '2023-11-07 16:21:27', '2023-11-07 16:21:27', '2023-11-07 16:21:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509303685952', '', **************, 11508998330944, 3, 0, 0, 11508994849856, 2, 0, '{}', '10967307334208', '2023-11-07 16:47:15', '2023-11-07 16:47:15', '2023-11-07 16:47:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509377979456', '', **************, 11508998330944, 4, 0, 0, 11508994849856, 2, 0, '{}', '10967307334208', '2023-11-07 16:56:55', '2023-11-07 16:56:55', '2023-11-07 16:56:55');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509413682240', '', **************, 11508998330944, 1, 0, 0, 11508994849856, 3, 0, '{}', '10967307334208', '2023-11-07 17:01:34', '2023-11-07 17:01:34', '2023-11-07 17:01:34');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509787736512', '', **************, 11508998330944, 5, 0, 0, 11508994849856, 4, 0, '{}', '10967307334208', '2023-11-07 17:50:17', '2023-11-07 17:50:17', '2023-11-07 17:50:17');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509793501376', '', **************, 11508998330944, 5, 0, 0, 11508994849856, 5, 0, '{}', '10967307334208', '2023-11-07 17:51:02', '2023-11-07 17:51:02', '2023-11-07 17:51:02');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11509928475840', '', **************, 11508998330944, 5, 0, 0, 11508994849856, 6, 0, '{}', '10967307334208', '2023-11-07 18:08:36', '2023-11-07 18:08:36', '2023-11-07 18:08:36');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574435336480', '', **************, 11508998330944, 5, 0, 0, 11508994849856, 6, 0, '{}', '4', '2023-11-13 14:07:56', '2023-11-13 14:07:56', '2023-11-13 14:07:56');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574435336481', NULL, **************, 11508998330944, 5, 11508994849856, 4, 11574435294368, 1, 0, '{}', '4', '2023-11-13 14:07:56', '2023-11-13 14:07:56', '2023-11-13 14:07:56');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574566391584', '', **************, 11276197971872, 1, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 14:25:00', '2023-11-13 14:25:00', '2023-11-13 14:25:00');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574566391585', NULL, **************, 11276197971872, 1, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 14:25:00', '2023-11-13 14:25:00', '2023-11-13 14:25:00');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574571196320', '', **************, 11508998330944, 6, 0, 0, 11508994849856, 6, 0, '{}', '4', '2023-11-13 14:25:37', '2023-11-13 14:25:37', '2023-11-13 14:25:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574629255328', '', **************, 11276197971872, 1, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 14:33:11', '2023-11-13 14:33:11', '2023-11-13 14:33:11');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574629255329', NULL, **************, 11276197971872, 1, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 14:33:11', '2023-11-13 14:33:11', '2023-11-13 14:33:11');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574629255330', NULL, **************, 11276197971872, 1, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 14:33:11', '2023-11-13 14:33:11', '2023-11-13 14:33:11');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574769131808', '', **************, 11276197971872, 1, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 14:51:24', '2023-11-13 14:51:24', '2023-11-13 14:51:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574769131809', NULL, **************, 11276197971872, 1, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 14:51:24', '2023-11-13 14:51:24', '2023-11-13 14:51:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574769131810', NULL, **************, 11276197971872, 1, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 14:51:24', '2023-11-13 14:51:24', '2023-11-13 14:51:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574769131811', NULL, **************, 11276197971872, 1, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 14:51:24', '2023-11-13 14:51:24', '2023-11-13 14:51:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574900130336', '', **************, 11276197971872, 2, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:08:27', '2023-11-13 15:08:27', '2023-11-13 15:08:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574900130337', NULL, **************, 11276197971872, 2, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:08:27', '2023-11-13 15:08:27', '2023-11-13 15:08:27');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574909448992', '', **************, 11276197971872, 2, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:09:40', '2023-11-13 15:09:40', '2023-11-13 15:09:40');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574909448993', NULL, **************, 11276197971872, 2, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:09:40', '2023-11-13 15:09:40', '2023-11-13 15:09:40');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11574909448994', NULL, **************, 11276197971872, 2, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:09:40', '2023-11-13 15:09:40', '2023-11-13 15:09:40');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575001668512', '', **************, 11276197971872, 3, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:21:41', '2023-11-13 15:21:41', '2023-11-13 15:21:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575001668513', NULL, **************, 11276197971872, 3, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:21:41', '2023-11-13 15:21:41', '2023-11-13 15:21:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575034288288', '', **************, 11276197971872, 3, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:25:55', '2023-11-13 15:25:55', '2023-11-13 15:25:55');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575034288289', NULL, **************, 11276197971872, 3, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:25:55', '2023-11-13 15:25:55', '2023-11-13 15:25:55');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575034288290', NULL, **************, 11276197971872, 3, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:25:55', '2023-11-13 15:25:55', '2023-11-13 15:25:55');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575037738016', '', **************, 11276197971872, 4, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:26:22', '2023-11-13 15:26:22', '2023-11-13 15:26:22');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575037738017', NULL, **************, 11276197971872, 4, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:26:22', '2023-11-13 15:26:22', '2023-11-13 15:26:22');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575042712992', '', **************, 11276197971872, 4, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:27:01', '2023-11-13 15:27:01', '2023-11-13 15:27:01');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575042712993', NULL, **************, 11276197971872, 4, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:27:01', '2023-11-13 15:27:01', '2023-11-13 15:27:01');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575042712994', NULL, **************, 11276197971872, 4, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:27:01', '2023-11-13 15:27:01', '2023-11-13 15:27:01');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575060526752', '', **************, 11276197971872, 4, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:29:20', '2023-11-13 15:29:20', '2023-11-13 15:29:20');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575060526753', NULL, **************, 11276197971872, 4, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:29:20', '2023-11-13 15:29:20', '2023-11-13 15:29:20');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575060526754', NULL, **************, 11276197971872, 4, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:29:20', '2023-11-13 15:29:20', '2023-11-13 15:29:20');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575060526755', NULL, **************, 11276197971872, 4, **************, 1, 11575060491168, 1, 0, '{}', '4', '2023-11-13 15:29:20', '2023-11-13 15:29:20', '2023-11-13 15:29:20');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575064104480', '', **************, 11276197971872, 5, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:29:48', '2023-11-13 15:29:48', '2023-11-13 15:29:48');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575064104481', NULL, **************, 11276197971872, 5, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:29:48', '2023-11-13 15:29:48', '2023-11-13 15:29:48');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575064104482', NULL, **************, 11276197971872, 5, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:29:48', '2023-11-13 15:29:48', '2023-11-13 15:29:48');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575064104483', NULL, **************, 11276197971872, 5, 0, 0, 11575060491168, 2, 0, '{}', '4', '2023-11-13 15:29:48', '2023-11-13 15:29:48', '2023-11-13 15:29:48');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575071259168', '', **************, 11276197971872, 6, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:30:44', '2023-11-13 15:30:44', '2023-11-13 15:30:44');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575071259169', NULL, **************, 11276197971872, 6, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:30:44', '2023-11-13 15:30:44', '2023-11-13 15:30:44');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575071259170', NULL, **************, 11276197971872, 6, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:30:44', '2023-11-13 15:30:44', '2023-11-13 15:30:44');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575072955424', '', **************, 11276197971872, 6, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:30:57', '2023-11-13 15:30:57', '2023-11-13 15:30:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575072955425', NULL, **************, 11276197971872, 6, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:30:57', '2023-11-13 15:30:57', '2023-11-13 15:30:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575072955426', NULL, **************, 11276197971872, 6, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:30:57', '2023-11-13 15:30:57', '2023-11-13 15:30:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575072955427', NULL, **************, 11276197971872, 6, 0, 0, 11575072923552, 1, 0, '{}', '4', '2023-11-13 15:30:57', '2023-11-13 15:30:57', '2023-11-13 15:30:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575076368288', '', **************, 11276197971872, 7, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:31:24', '2023-11-13 15:31:24', '2023-11-13 15:31:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575076368289', NULL, **************, 11276197971872, 7, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:31:24', '2023-11-13 15:31:24', '2023-11-13 15:31:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575076368290', NULL, **************, 11276197971872, 7, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:31:24', '2023-11-13 15:31:24', '2023-11-13 15:31:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575077895584', '', **************, 11276197971872, 8, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:31:36', '2023-11-13 15:31:36', '2023-11-13 15:31:36');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575077895585', NULL, **************, 11276197971872, 8, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:31:36', '2023-11-13 15:31:36', '2023-11-13 15:31:36');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575078526752', '', **************, 11276197971872, 8, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:31:41', '2023-11-13 15:31:41', '2023-11-13 15:31:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575078526753', NULL, **************, 11276197971872, 8, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:31:41', '2023-11-13 15:31:41', '2023-11-13 15:31:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575078526754', NULL, **************, 11276197971872, 8, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:31:41', '2023-11-13 15:31:41', '2023-11-13 15:31:41');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575120142624', '', **************, 11276197971872, 9, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:37:06', '2023-11-13 15:37:06', '2023-11-13 15:37:06');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575120142625', NULL, **************, 11276197971872, 9, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:37:06', '2023-11-13 15:37:06', '2023-11-13 15:37:06');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575124091296', '', **************, 11276197971872, 9, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:37:37', '2023-11-13 15:37:37', '2023-11-13 15:37:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575124091297', NULL, **************, 11276197971872, 9, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:37:37', '2023-11-13 15:37:37', '2023-11-13 15:37:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575124091298', NULL, **************, 11276197971872, 9, 11574566356256, 1, 11575124055840, 1, 0, '{}', '4', '2023-11-13 15:37:37', '2023-11-13 15:37:37', '2023-11-13 15:37:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575131528352', '', **************, 11276197971872, 10, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:38:35', '2023-11-13 15:38:35', '2023-11-13 15:38:35');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575131528353', NULL, **************, 11276197971872, 10, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:38:35', '2023-11-13 15:38:35', '2023-11-13 15:38:35');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575139418400', '', **************, 11276197971872, 10, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:39:37', '2023-11-13 15:39:37', '2023-11-13 15:39:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575139418401', NULL, **************, 11276197971872, 10, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:39:37', '2023-11-13 15:39:37', '2023-11-13 15:39:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575139418402', NULL, **************, 11276197971872, 10, 11574566356256, 1, 11574629217824, 1, 0, '{}', '4', '2023-11-13 15:39:37', '2023-11-13 15:39:37', '2023-11-13 15:39:37');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575143632672', '', **************, 11276197971872, 11, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:40:10', '2023-11-13 15:40:10', '2023-11-13 15:40:10');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575143632673', NULL, **************, 11276197971872, 11, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:40:10', '2023-11-13 15:40:10', '2023-11-13 15:40:10');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575146652064', '', **************, 11276197971872, 11, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:40:33', '2023-11-13 15:40:33', '2023-11-13 15:40:33');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575146652065', NULL, **************, 11276197971872, 11, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:40:33', '2023-11-13 15:40:33', '2023-11-13 15:40:33');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575146652066', NULL, **************, 11276197971872, 11, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:40:33', '2023-11-13 15:40:33', '2023-11-13 15:40:33');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575199896096', '', **************, 11276197971872, 11, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:47:29', '2023-11-13 15:47:29', '2023-11-13 15:47:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575199896097', NULL, **************, 11276197971872, 11, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:47:29', '2023-11-13 15:47:29', '2023-11-13 15:47:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575199896098', NULL, **************, 11276197971872, 11, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:47:29', '2023-11-13 15:47:29', '2023-11-13 15:47:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575199896099', NULL, **************, 11276197971872, 11, 11574566356256, 1, 11574629217830, 1, 0, '{}', '4', '2023-11-13 15:47:29', '2023-11-13 15:47:29', '2023-11-13 15:47:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575206932512', '', **************, 11276197971872, 12, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:48:24', '2023-11-13 15:48:24', '2023-11-13 15:48:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575206932513', NULL, **************, 11276197971872, 12, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:48:24', '2023-11-13 15:48:24', '2023-11-13 15:48:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575206932514', NULL, **************, 11276197971872, 12, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:48:24', '2023-11-13 15:48:24', '2023-11-13 15:48:24');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575209241632', '', **************, 11276197971872, 12, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:48:42', '2023-11-13 15:48:42', '2023-11-13 15:48:42');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575209241633', NULL, **************, 11276197971872, 12, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:48:42', '2023-11-13 15:48:42', '2023-11-13 15:48:42');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575209241634', NULL, **************, 11276197971872, 12, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:48:42', '2023-11-13 15:48:42', '2023-11-13 15:48:42');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575209241635', NULL, **************, 11276197971872, 12, 11574566356256, 1, 11574629217830, 1, 0, '{}', '4', '2023-11-13 15:48:42', '2023-11-13 15:48:42', '2023-11-13 15:48:42');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575212389408', '', **************, 11276197971872, 13, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:49:07', '2023-11-13 15:49:07', '2023-11-13 15:49:07');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575212389409', NULL, **************, 11276197971872, 13, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:49:07', '2023-11-13 15:49:07', '2023-11-13 15:49:07');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575212389410', NULL, **************, 11276197971872, 13, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:49:07', '2023-11-13 15:49:07', '2023-11-13 15:49:07');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575213414432', '', **************, 11276197971872, 13, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:49:15', '2023-11-13 15:49:15', '2023-11-13 15:49:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575213414433', NULL, **************, 11276197971872, 13, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:49:15', '2023-11-13 15:49:15', '2023-11-13 15:49:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575213414434', NULL, **************, 11276197971872, 13, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:49:15', '2023-11-13 15:49:15', '2023-11-13 15:49:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575213414435', NULL, **************, 11276197971872, 13, 11574566356256, 1, 11574629217831, 1, 0, '{}', '4', '2023-11-13 15:49:15', '2023-11-13 15:49:15', '2023-11-13 15:49:15');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575215221920', '', **************, 11276197971872, 14, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:49:29', '2023-11-13 15:49:29', '2023-11-13 15:49:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575215221921', NULL, **************, 11276197971872, 14, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:49:29', '2023-11-13 15:49:29', '2023-11-13 15:49:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575215221922', NULL, **************, 11276197971872, 14, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:49:29', '2023-11-13 15:49:29', '2023-11-13 15:49:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575215221923', NULL, **************, 11276197971872, 14, 0, 0, 11574629217831, 2, 0, '{}', '4', '2023-11-13 15:49:29', '2023-11-13 15:49:29', '2023-11-13 15:49:29');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575254877856', '', **************, 11276197971872, 15, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:54:39', '2023-11-13 15:54:39', '2023-11-13 15:54:39');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575254877857', NULL, **************, 11276197971872, 15, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:54:39', '2023-11-13 15:54:39', '2023-11-13 15:54:39');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575254877858', NULL, **************, 11276197971872, 15, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:54:39', '2023-11-13 15:54:39', '2023-11-13 15:54:39');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575254877859', NULL, **************, 11276197971872, 15, 0, 0, 11574629217831, 3, 0, '{}', '4', '2023-11-13 15:54:39', '2023-11-13 15:54:39', '2023-11-13 15:54:39');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575272535200', '', **************, 11276197971872, 16, 0, 0, 11276191674144, 1, 0, '{}', '4', '2023-11-13 15:56:57', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575272535201', NULL, **************, 11276197971872, 16, 11276191674144, 1, 11574566356256, 1, 0, '{}', '4', '2023-11-13 15:56:57', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575272535202', NULL, **************, 11276197971872, 16, 11574566356256, 1, 11575146272416, 1, 0, '{}', '4', '2023-11-13 15:56:57', '2023-11-13 15:56:57', '2023-11-13 15:56:57');
INSERT INTO `t_ds_process_task_relation_log` VALUES ('11575272535203', NULL, **************, 11276197971872, 16, 0, 0, 11574629217831, 4, 0, '{}', '4', '2023-11-13 15:56:57', '2023-11-13 15:56:57', '2023-11-13 15:56:57');

-- ----------------------------
-- Table structure for t_ds_project
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_project`;
CREATE TABLE `t_ds_project`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'project name',
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'creator id',
  `flag` tinyint(0) NULL DEFAULT 1 COMMENT '0 not available, 1 available',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_code`(`code`) USING BTREE,
  UNIQUE INDEX `unique_name`(`name`) USING BTREE,
  INDEX `user_id_index`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_project
-- ----------------------------
INSERT INTO `t_ds_project` VALUES ('10811866587712', '测试调度', **************, '', '4', 1, '2023-09-05 15:15:08', '2023-09-05 15:15:08');
INSERT INTO `t_ds_project` VALUES ('11199696680256', 'windows', 11199696680000, '', '4', 1, '2023-10-10 16:53:50', '2023-10-10 16:53:50');

-- ----------------------------
-- Table structure for t_ds_queue
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_queue`;
CREATE TABLE `t_ds_queue`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `queue_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'queue name',
  `queue` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'yarn queue name',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_queue_name`(`queue_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_queue
-- ----------------------------
INSERT INTO `t_ds_queue` VALUES ('1', 'default', 'default', NULL, NULL);

-- ----------------------------
-- Table structure for t_ds_relation_datasource_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_datasource_user`;
CREATE TABLE `t_ds_relation_datasource_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `datasource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'data source id',
  `perm` int(0) NULL DEFAULT 1 COMMENT 'limits of authority',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_datasource_user
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_relation_namespace_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_namespace_user`;
CREATE TABLE `t_ds_relation_namespace_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `namespace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'namespace id',
  `perm` int(0) NULL DEFAULT 1 COMMENT 'limits of authority',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `namespace_user_unique`(`user_id`, `namespace_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_namespace_user
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_relation_process_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_process_instance`;
CREATE TABLE `t_ds_relation_process_instance`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `parent_process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'parent process instance id',
  `parent_task_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'parent process instance id',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'child process instance id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_process_task`(`parent_process_instance_id`, `parent_task_instance_id`) USING BTREE,
  INDEX `idx_process_instance_id`(`process_instance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_process_instance
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_relation_project_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_project_user`;
CREATE TABLE `t_ds_relation_project_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `project_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'project id',
  `perm` int(0) NULL DEFAULT 1 COMMENT 'limits of authority',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_uid_pid`(`user_id`, `project_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_project_user
-- ----------------------------
INSERT INTO `t_ds_relation_project_user` VALUES ('10967308667840', '10967307334208', '10811866587712', 7, '2023-09-19 16:34:59', '2023-09-19 16:34:59');

-- ----------------------------
-- Table structure for t_ds_relation_resources_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_resources_user`;
CREATE TABLE `t_ds_relation_resources_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `resources_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'resource id',
  `perm` int(0) NULL DEFAULT 1 COMMENT 'limits of authority',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_resources_user
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_relation_rule_execute_sql
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_rule_execute_sql`;
CREATE TABLE `t_ds_relation_rule_execute_sql`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `execute_sql_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_rule_execute_sql
-- ----------------------------
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('1', '1', '1', '2022-10-13 10:18:18', '2022-10-13 10:18:18');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('10', '8', '12', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('11', '9', '13', '2022-10-13 10:18:20', '2022-10-13 10:18:20');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('12', '9', '14', '2022-10-13 10:18:20', '2022-10-13 10:18:20');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('13', '10', '15', '2022-10-13 10:18:20', '2022-10-13 10:18:20');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('14', '1', '16', '2022-10-13 10:18:20', '2022-10-13 10:18:20');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('15', '5', '17', '2022-10-13 10:18:20', '2022-10-13 10:18:20');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('2', '3', '3', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('3', '5', '4', '2022-10-13 10:18:18', '2022-10-13 10:18:18');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('4', '3', '8', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('5', '6', '6', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('6', '6', '7', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('7', '7', '9', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('8', '7', '10', '2022-10-13 10:18:19', '2022-10-13 10:18:19');
INSERT INTO `t_ds_relation_rule_execute_sql` VALUES ('9', '8', '11', '2022-10-13 10:18:19', '2022-10-13 10:18:19');

-- ----------------------------
-- Table structure for t_ds_relation_rule_input_entry
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_rule_input_entry`;
CREATE TABLE `t_ds_relation_rule_input_entry`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `rule_input_entry_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `values_map` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `index` int(0) NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_rule_input_entry
-- ----------------------------
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('1', '1', '1', NULL, 1, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('10', '1', '10', NULL, 10, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('100', '7', '7', NULL, 7, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('101', '7', '8', NULL, 8, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('102', '7', '9', NULL, 9, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('103', '7', '10', NULL, 10, '2022-10-13 10:18:34', '2022-10-13 10:18:34');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('104', '7', '17', NULL, 11, '2022-10-13 10:18:34', '2022-10-13 10:18:34');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('105', '7', '19', NULL, 12, '2022-10-13 10:18:34', '2022-10-13 10:18:34');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('108', '8', '1', NULL, 1, '2022-10-13 10:18:34', '2022-10-13 10:18:34');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('109', '8', '2', NULL, 2, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('11', '1', '17', '', 11, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('110', '8', '3', NULL, 3, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('111', '8', '4', NULL, 4, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('112', '8', '5', NULL, 5, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('113', '8', '6', '{\"statistics_name\":\"timeliness_count.timeliness\"}', 6, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('114', '8', '26', NULL, 8, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('115', '8', '27', NULL, 9, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('116', '8', '7', NULL, 10, '2022-10-13 10:18:35', '2022-10-13 10:18:35');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('117', '8', '8', NULL, 11, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('118', '8', '9', NULL, 12, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('119', '8', '10', NULL, 13, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('12', '1', '19', NULL, 12, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('120', '8', '17', NULL, 14, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('121', '8', '19', NULL, 15, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('124', '9', '1', NULL, 1, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('125', '9', '2', NULL, 2, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('126', '9', '3', NULL, 3, '2022-10-13 10:18:36', '2022-10-13 10:18:36');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('127', '9', '4', NULL, 4, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('128', '9', '5', NULL, 5, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('129', '9', '6', '{\"statistics_name\":\"enum_count.enums\"}', 6, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('13', '2', '1', NULL, 1, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('130', '9', '28', NULL, 7, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('131', '9', '7', NULL, 8, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('132', '9', '8', NULL, 9, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('133', '9', '9', NULL, 10, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('134', '9', '10', NULL, 11, '2022-10-13 10:18:37', '2022-10-13 10:18:37');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('135', '9', '17', NULL, 12, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('136', '9', '19', NULL, 13, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('139', '10', '1', NULL, 1, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('14', '2', '2', NULL, 2, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('140', '10', '2', NULL, 2, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('141', '10', '3', NULL, 3, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('142', '10', '4', NULL, 4, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('143', '10', '6', '{\"statistics_name\":\"table_count.total\"}', 6, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('144', '10', '7', NULL, 7, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('145', '10', '8', NULL, 8, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('146', '10', '9', NULL, 9, '2022-10-13 10:18:38', '2022-10-13 10:18:38');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('147', '10', '10', NULL, 10, '2022-10-13 10:18:39', '2022-10-13 10:18:39');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('148', '10', '17', NULL, 11, '2022-10-13 10:18:39', '2022-10-13 10:18:39');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('149', '10', '19', NULL, 12, '2022-10-13 10:18:39', '2022-10-13 10:18:39');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('15', '2', '3', NULL, 3, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('150', '8', '29', NULL, 7, '2022-10-13 10:18:39', '2022-10-13 10:18:39');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('16', '2', '6', '{\"is_show\":\"true\",\"can_edit\":\"true\"}', 4, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('17', '2', '16', NULL, 5, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('18', '2', '4', NULL, 6, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('19', '2', '7', NULL, 7, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('2', '1', '2', NULL, 2, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('20', '2', '8', NULL, 8, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('21', '2', '9', NULL, 9, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('22', '2', '10', NULL, 10, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('24', '2', '19', NULL, 12, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('25', '3', '1', NULL, 1, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('26', '3', '2', NULL, 2, '2022-10-13 10:18:23', '2022-10-13 10:18:23');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('27', '3', '3', NULL, 3, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('28', '3', '4', NULL, 4, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('29', '3', '11', NULL, 5, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('3', '1', '3', NULL, 3, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('30', '3', '12', NULL, 6, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('31', '3', '13', NULL, 7, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('32', '3', '14', NULL, 8, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('33', '3', '15', NULL, 9, '2022-10-13 10:18:24', '2022-10-13 10:18:24');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('34', '3', '7', NULL, 10, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('35', '3', '8', NULL, 11, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('36', '3', '9', NULL, 12, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('37', '3', '10', NULL, 13, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('38', '3', '17', '{\"comparison_name\":\"total_count.total\"}', 14, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('39', '3', '19', NULL, 15, '2022-10-13 10:18:25', '2022-10-13 10:18:25');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('4', '1', '4', NULL, 4, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('40', '4', '1', NULL, 1, '2022-10-13 10:18:26', '2022-10-13 10:18:26');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('41', '4', '2', NULL, 2, '2022-10-13 10:18:26', '2022-10-13 10:18:26');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('42', '4', '3', NULL, 3, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('43', '4', '6', '{\"is_show\":\"true\",\"can_edit\":\"true\"}', 4, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('44', '4', '16', NULL, 5, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('45', '4', '11', NULL, 6, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('46', '4', '12', NULL, 7, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('47', '4', '13', NULL, 8, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('48', '4', '17', '{\"is_show\":\"true\",\"can_edit\":\"true\"}', 9, '2022-10-13 10:18:27', '2022-10-13 10:18:27');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('49', '4', '18', NULL, 10, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('5', '1', '5', NULL, 5, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('50', '4', '7', NULL, 11, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('51', '4', '8', NULL, 12, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('52', '4', '9', NULL, 13, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('53', '4', '10', NULL, 14, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('6', '1', '6', '{\"statistics_name\":\"null_count.nulls\"}', 6, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('62', '3', '6', '{\"statistics_name\":\"miss_count.miss\"}', 18, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('63', '5', '1', NULL, 1, '2022-10-13 10:18:28', '2022-10-13 10:18:28');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('64', '5', '2', NULL, 2, '2022-10-13 10:18:29', '2022-10-13 10:18:29');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('65', '5', '3', NULL, 3, '2022-10-13 10:18:29', '2022-10-13 10:18:29');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('66', '5', '4', NULL, 4, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('67', '5', '5', NULL, 5, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('68', '5', '6', '{\"statistics_name\":\"invalid_length_count.valids\"}', 6, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('69', '5', '24', NULL, 7, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('7', '1', '7', NULL, 7, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('70', '5', '23', NULL, 8, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('71', '5', '7', NULL, 9, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('72', '5', '8', NULL, 10, '2022-10-13 10:18:30', '2022-10-13 10:18:30');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('73', '5', '9', NULL, 11, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('74', '5', '10', NULL, 12, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('75', '5', '17', '', 13, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('76', '5', '19', NULL, 14, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('79', '6', '1', NULL, 1, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('8', '1', '8', NULL, 8, '2022-10-13 10:18:21', '2022-10-13 10:18:21');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('80', '6', '2', NULL, 2, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('81', '6', '3', NULL, 3, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('82', '6', '4', NULL, 4, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('83', '6', '5', NULL, 5, '2022-10-13 10:18:31', '2022-10-13 10:18:31');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('84', '6', '6', '{\"statistics_name\":\"duplicate_count.duplicates\"}', 6, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('85', '6', '7', NULL, 7, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('86', '6', '8', NULL, 8, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('87', '6', '9', NULL, 9, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('88', '6', '10', NULL, 10, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('89', '6', '17', '', 11, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('9', '1', '9', NULL, 9, '2022-10-13 10:18:22', '2022-10-13 10:18:22');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('90', '6', '19', NULL, 12, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('93', '7', '1', NULL, 1, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('94', '7', '2', NULL, 2, '2022-10-13 10:18:32', '2022-10-13 10:18:32');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('95', '7', '3', NULL, 3, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('96', '7', '4', NULL, 4, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('97', '7', '5', NULL, 5, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('98', '7', '6', '{\"statistics_name\":\"regexp_count.regexps\"}', 6, '2022-10-13 10:18:33', '2022-10-13 10:18:33');
INSERT INTO `t_ds_relation_rule_input_entry` VALUES ('99', '7', '25', NULL, 5, '2022-10-13 10:18:33', '2022-10-13 10:18:33');

-- ----------------------------
-- Table structure for t_ds_relation_udfs_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_relation_udfs_user`;
CREATE TABLE `t_ds_relation_udfs_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'userid',
  `udf_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'udf id',
  `perm` int(0) NULL DEFAULT 1 COMMENT 'limits of authority',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_relation_udfs_user
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_resources
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_resources`;
CREATE TABLE `t_ds_resources`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alias` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alias',
  `file_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'file name',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'user id',
  `type` tinyint(0) NULL DEFAULT NULL COMMENT 'resource type,0:FILE，1:UDF',
  `size` bigint(0) NULL DEFAULT NULL COMMENT 'resource size',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `full_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_directory` tinyint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_resources
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_schedules
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_schedules`;
CREATE TABLE `t_ds_schedules`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `process_definition_code` bigint(0) NOT NULL COMMENT 'process definition code',
  `start_time` datetime(0) NOT NULL COMMENT 'start time',
  `end_time` datetime(0) NOT NULL COMMENT 'end time',
  `timezone_id` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'schedule timezone id',
  `crontab` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'crontab description',
  `failure_strategy` tinyint(0) NOT NULL COMMENT 'failure strategy. 0:end,1:continue',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `release_state` tinyint(0) NOT NULL COMMENT 'release state. 0:offline,1:online ',
  `warning_type` tinyint(0) NOT NULL COMMENT 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent',
  `warning_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'alert group id',
  `process_instance_priority` int(0) NULL DEFAULT 2 COMMENT 'process instance priority：0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'worker group id',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  `calendar_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日历表ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_schedules
-- ----------------------------
INSERT INTO `t_ds_schedules` VALUES ('10844944116800', 10844574533184, '2023-09-08 00:00:00', '2123-09-08 00:00:00', 'Asia/Shanghai', '0 * * * * ? *', 1, '4', 0, 0, '0', 2, 'default', -1, '2023-09-08 15:02:06', '2023-09-11 10:44:25', NULL);
INSERT INTO `t_ds_schedules` VALUES ('11274740615456', 11199601127968, '2023-10-17 00:00:00', '2123-10-17 00:00:00', 'Asia/Shanghai', '0 0 * * * ? *', 1, '1', 0, 0, '0', 2, 'default', -1, '2023-10-17 11:45:11', '2023-10-17 11:45:11', NULL);
INSERT INTO `t_ds_schedules` VALUES ('11421255573952', 11421193780800, '2023-10-30 00:00:00', '2123-10-30 00:00:00', 'Asia/Shanghai', '0 0 * * * ? *', 1, '1', 0, 0, '0', 2, 'default', -1, '2023-10-30 17:42:39', '2023-10-30 17:42:39', NULL);

-- ----------------------------
-- Table structure for t_ds_session
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_session`;
CREATE TABLE `t_ds_session`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'user id',
  `ip` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ip',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT 'last login time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_session
-- ----------------------------
INSERT INTO `t_ds_session` VALUES ('6e4da449-dc75-4f5e-934d-564940e2c95a', '4', '127.0.0.1', '2023-11-14 13:58:54');
INSERT INTO `t_ds_session` VALUES ('e8d58ca8-412f-4176-b4d6-4d7d8263dea1', '10967307334208', '127.0.0.1', '2023-11-07 16:06:43');

-- ----------------------------
-- Table structure for t_ds_task_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_definition`;
CREATE TABLE `t_ds_task_definition`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition name',
  `version` int(0) NULL DEFAULT 0 COMMENT 'task definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition creator id',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int(0) NULL DEFAULT 0 COMMENT 'task execute type: 0-batch, 1-stream',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'job custom parameters',
  `flag` tinyint(0) NULL DEFAULT NULL COMMENT '0 not available, 1 available',
  `task_priority` tinyint(0) NULL DEFAULT 2 COMMENT 'job priority',
  `worker_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker grouping',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `fail_retry_times` int(0) NULL DEFAULT NULL COMMENT 'number of failed retries',
  `fail_retry_interval` int(0) NULL DEFAULT NULL COMMENT 'failed retry interval',
  `timeout_flag` tinyint(0) NULL DEFAULT 0 COMMENT 'timeout flag:0 close, 1 open',
  `timeout_notify_strategy` tinyint(0) NULL DEFAULT NULL COMMENT 'timeout notification policy: 0 warning, 1 fail',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'timeout length,unit: minute',
  `delay_time` int(0) NULL DEFAULT 0 COMMENT 'delay execution time,unit: minute',
  `resource_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'resource id, separated by comma',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task group id',
  `task_group_priority` tinyint(0) NULL DEFAULT 0 COMMENT 'task group priority',
  `cpu_quota` int(0) NOT NULL DEFAULT -1 COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int(0) NOT NULL DEFAULT -1 COMMENT 'MemoryMax(MB): -1:Infinity',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`, `code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7729538429091 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_definition
-- ----------------------------
INSERT INTO `t_ds_task_definition` VALUES (7729538429051, 10811878809280, '请求百度', 1, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"https://www.baidu.com/sugrec?&prod=pc_his&from=pc_web&json=1&sid=39226_39278_39223_39285_39097_39261_39269_39240_39233_26350_39238_39224_39149&hisdata=&_t=1693898349736&csor=0\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-05 15:23:32', '2023-09-05 15:23:32');
INSERT INTO `t_ds_task_definition` VALUES (7729538429052, 10812422708672, '请求自定义接口', 1, '', **************, '1', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-05 16:28:08', '2023-09-05 16:28:08');
INSERT INTO `t_ds_task_definition` VALUES (7729538429053, 10812436419776, '自定义接口调试', 1, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-05 16:29:44', '2023-09-05 16:29:44');
INSERT INTO `t_ds_task_definition` VALUES (7729538429054, 10812663635136, 'shell测试', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping -t www.baidu.com \",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_task_definition` VALUES (7729538429055, 10821244252096, 'shell测试生成的文件', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-06 11:37:09', '2023-09-06 11:37:09');
INSERT INTO `t_ds_task_definition` VALUES (7729538429056, 10835090078656, 'datax调度测试', 1, '', **************, '4', 'DATAX', 0, '{\"localParams\":[],\"resourceList\":[],\"customConfig\":1,\"json\":\"json111\",\"xms\":1,\"xmx\":1}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_task_definition` VALUES (7729538429057, 10844570462400, 'shell输出', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_task_definition` VALUES (7729538429058, 10953569347136, '测试001', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"baidu.com\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-18 10:46:01', '2023-09-18 10:46:01');
INSERT INTO `t_ds_task_definition` VALUES (7729538429059, 10953574443072, '测试002', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"测试002\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_task_definition` VALUES (7729538429060, 10989164532800, 'shell01', 1, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-21 16:04:10', '2023-09-21 16:04:10');
INSERT INTO `t_ds_task_definition` VALUES (7729538429061, 11177851623200, '节点1', 1, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"xxx\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-08 17:31:24', '2023-10-08 17:31:24');
INSERT INTO `t_ds_task_definition` VALUES (*************, **************, 'ST测试', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 10:07:46', '2023-10-10 10:07:46');
INSERT INTO `t_ds_task_definition` VALUES (7729538429063, **************, 'st0011123', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 14:46:33', '2023-10-10 14:46:33');
INSERT INTO `t_ds_task_definition` VALUES (*************, **************, 'linux_st', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_task_definition` VALUES (*************, **************, 'st_cluster', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_task_definition` VALUES (7729538429066, 11199699252928, '001', 1, '', 11199696680000, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_task_definition` VALUES (7729538429067, 11199717663680, 's001', 1, '', 11199696680000, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-10 16:56:53', '2023-10-10 16:56:53');
INSERT INTO `t_ds_task_definition` VALUES (7729538429068, 11276191674144, 'testadd', 1, '', **************, '1', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_task_definition` VALUES (7729538429069, 11421174732096, '001', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:34:36');
INSERT INTO `t_ds_task_definition` VALUES (7729538429070, 11421191059392, '002', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:34:36');
INSERT INTO `t_ds_task_definition` VALUES (7729538429071, 11421196238912, '123', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"234234\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:37:40', '2023-10-30 17:37:40');
INSERT INTO `t_ds_task_definition` VALUES (7729538429072, 11421228664000, '123123', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:39:16', '2023-10-30 17:39:16');
INSERT INTO `t_ds_task_definition` VALUES (7729538429073, 11421313640256, '01', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_task_definition` VALUES (7729538429074, 11421314732992, '002', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"4561\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_task_definition` VALUES (7729538429075, 11508994849856, '测试调度01', 4, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000测试05\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, 0, 0, 0, '', NULL, 0, -1, -1, '2023-11-07 16:07:29', '2023-11-07 17:50:29');
INSERT INTO `t_ds_task_definition` VALUES (7729538429077, 11574566356256, '测试新增', 1, 'x', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{x}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-11-13 14:25:00', '2023-11-13 14:25:00');
INSERT INTO `t_ds_task_definition` VALUES (7729538429087, 11575146272416, '新增测试1', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-11-13 15:40:33', '2023-11-13 15:40:33');
INSERT INTO `t_ds_task_definition` VALUES (7729538429090, 11574629217831, '新增111测试', 4, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxxqqqq111}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-11-13 15:49:15', '2023-11-13 15:56:55');

-- ----------------------------
-- Table structure for t_ds_task_definition_log
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_definition_log`;
CREATE TABLE `t_ds_task_definition_log`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition name',
  `version` int(0) NULL DEFAULT 0 COMMENT 'task definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition creator id',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int(0) NULL DEFAULT 0 COMMENT 'task execute type: 0-batch, 1-stream',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'job custom parameters',
  `flag` tinyint(0) NULL DEFAULT NULL COMMENT '0 not available, 1 available',
  `task_priority` tinyint(0) NULL DEFAULT 2 COMMENT 'job priority',
  `worker_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker grouping',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `fail_retry_times` int(0) NULL DEFAULT NULL COMMENT 'number of failed retries',
  `fail_retry_interval` int(0) NULL DEFAULT NULL COMMENT 'failed retry interval',
  `timeout_flag` tinyint(0) NULL DEFAULT 0 COMMENT 'timeout flag:0 close, 1 open',
  `timeout_notify_strategy` tinyint(0) NULL DEFAULT NULL COMMENT 'timeout notification policy: 0 warning, 1 fail',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'timeout length,unit: minute',
  `delay_time` int(0) NULL DEFAULT 0 COMMENT 'delay execution time,unit: minute',
  `resource_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'resource id, separated by comma',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'operator user id',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task group id',
  `task_group_priority` tinyint(0) NULL DEFAULT 0 COMMENT 'task group priority',
  `operate_time` datetime(0) NULL DEFAULT NULL COMMENT 'operate time',
  `cpu_quota` int(0) NOT NULL DEFAULT -1 COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int(0) NOT NULL DEFAULT -1 COMMENT 'MemoryMax(MB): -1:Infinity',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_code_version`(`code`, `version`) USING BTREE,
  INDEX `idx_project_code`(`project_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7729538429411 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_definition_log
-- ----------------------------
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429326, 10811878809280, '请求百度', 1, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"https://www.baidu.com/sugrec?&prod=pc_his&from=pc_web&json=1&sid=39226_39278_39223_39285_39097_39261_39269_39240_39233_26350_39238_39224_39149&hisdata=&_t=1693898349736&csor=0\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-05 15:23:32', -1, -1, '2023-09-05 15:23:32', '2023-09-05 15:23:32');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429327, 10812422708672, '请求自定义接口', 1, '', **************, '1', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-09-05 16:28:08', -1, -1, '2023-09-05 16:28:08', '2023-09-05 16:28:08');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429328, 10812436419776, '自定义接口调试', 1, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-05 16:29:44', -1, -1, '2023-09-05 16:29:44', '2023-09-05 16:29:44');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429329, 10812436419776, '自定义接口调试', 2, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-05 16:31:22', -1, -1, '2023-09-05 16:29:44', '2023-09-05 16:31:22');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429330, 10812436419776, '自定义接口调试', 3, '', **************, '4', 'HTTP', 0, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-05 16:41:17', -1, -1, '2023-09-05 16:29:44', '2023-09-05 16:41:17');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429331, 10812663635136, 'shell测试', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping -t www.baidu.com \",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-05 17:00:59', -1, -1, '2023-09-05 17:00:59', '2023-09-05 17:00:59');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429332, 10821244252096, 'shell测试生成的文件', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-06 11:37:09', -1, -1, '2023-09-06 11:37:09', '2023-09-06 11:37:09');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429333, 10835090078656, 'datax调度测试', 1, '', **************, '4', 'DATAX', 0, '{\"localParams\":[],\"resourceList\":[],\"customConfig\":1,\"json\":\"json111\",\"xms\":1,\"xmx\":1}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-07 17:39:40', -1, -1, '2023-09-07 17:39:40', '2023-09-07 17:39:40');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429334, 10844570462400, 'shell输出', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-08 14:13:59', -1, -1, '2023-09-08 14:13:59', '2023-09-08 14:13:59');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429335, 10953569347136, '测试001', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"baidu.com\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-18 10:46:01', -1, -1, '2023-09-18 10:46:01', '2023-09-18 10:46:01');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429336, 10953574443072, '测试002', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"测试002\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-18 10:46:41', -1, -1, '2023-09-18 10:46:41', '2023-09-18 10:46:41');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429337, 10989164532800, 'shell01', 1, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-09-21 16:04:10', -1, -1, '2023-09-21 16:04:10', '2023-09-21 16:04:10');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429338, 10989164532800, 'shell01', 2, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-09-21 17:10:22', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:10:22');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429339, 10989164532800, 'shell01', 3, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-09-21 17:21:38', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:21:38');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429340, 10989164532800, 'shell01', 4, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-21 17:46:33', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:46:33');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429341, 10989164532800, 'shell01', 5, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-21 17:49:33', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:49:33');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429342, 10989164532800, 'shell01', 6, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-21 17:53:38', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:53:38');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429343, 10989164532800, 'shell01', 7, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-21 17:55:35', -1, -1, '2023-09-21 16:04:10', '2023-09-21 17:55:35');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429344, 10989164532800, 'shell01', 8, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-09-21 18:01:35', -1, -1, '2023-09-21 16:04:10', '2023-09-21 18:01:35');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429345, 11177851623200, '节点1', 1, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"xxx\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-08 17:31:24', -1, -1, '2023-10-08 17:31:24', '2023-10-08 17:31:24');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429346, 11177851623200, '节点1', 2, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"xxx\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-08 17:38:22', -1, -1, '2023-10-08 17:31:24', '2023-10-08 17:38:22');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429347, 11177851623200, '节点1', 3, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"xxx\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-08 18:03:39', -1, -1, '2023-10-08 17:31:24', '2023-10-08 18:03:39');
INSERT INTO `t_ds_task_definition_log` VALUES (*************, **************, 'ST测试', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 10:07:46', -1, -1, '2023-10-10 10:07:46', '2023-10-10 10:07:46');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429349, **************, 'st0011123', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 14:46:33', -1, -1, '2023-10-10 14:46:33', '2023-10-10 14:46:33');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429350, **************, 'st0011123', 2, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 14:56:21', -1, -1, '2023-10-10 14:46:33', '2023-10-10 14:56:21');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429351, **************, 'st0011123', 3, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 14:56:31', -1, -1, '2023-10-10 14:46:33', '2023-10-10 14:56:31');
INSERT INTO `t_ds_task_definition_log` VALUES (*************, **************, 'st0011123', 4, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 14:57:01', -1, -1, '2023-10-10 14:46:33', '2023-10-10 14:57:01');
INSERT INTO `t_ds_task_definition_log` VALUES (*************, **************, 'linux_st', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 16:09:47', -1, -1, '2023-10-10 16:09:47', '2023-10-10 16:09:47');
INSERT INTO `t_ds_task_definition_log` VALUES (*************, **************, 'st_cluster', 1, '', **************, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 16:41:24', -1, -1, '2023-10-10 16:41:24', '2023-10-10 16:41:24');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429355, 11199699252928, '001', 1, '', 11199696680000, '4', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 16:54:38', -1, -1, '2023-10-10 16:54:38', '2023-10-10 16:54:38');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429356, 11199717663680, 's001', 1, '', 11199696680000, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-10 16:56:53', -1, -1, '2023-10-10 16:56:53', '2023-10-10 16:56:53');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429357, 11276191674144, 'testadd', 1, '', **************, '1', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    execution.parallelism = 1\\n}\\n\\nsource {\\n    FakeSourceStream {\\n        result_table_name = \\\"fake\\\"\\n        field_name = \\\"name,age\\\"\\n    }\\n}\\n\\ntransform {\\n    sql {\\n        sql = \\\"select name,age from fake\\\"\\n    }\\n}\\n\\nsink {\\n    ConsoleSink {}\\n}\",\"resourceList\":[],\"engine\":\"FLINK\",\"useCustom\":true,\"runMode\":\"RUN\"}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-17 14:54:57', -1, -1, '2023-10-17 14:54:57', '2023-10-17 14:54:57');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429358, 10989164532800, 'shell01', 9, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 14:45:25', -1, -1, '2023-09-21 16:04:10', '2023-10-23 14:45:25');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429359, 10989164532800, 'shell01', 10, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 14:47:25', -1, -1, '2023-09-21 16:04:10', '2023-10-23 14:47:25');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429360, 10989164532800, 'shell01', 11, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 14:54:09', -1, -1, '2023-09-21 16:04:10', '2023-10-23 14:54:09');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429361, 10989164532800, 'shell01', 12, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 14:56:31', -1, -1, '2023-09-21 16:04:10', '2023-10-23 14:56:31');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429362, 10989164532800, 'shell01', 13, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 14:58:19', -1, -1, '2023-09-21 16:04:10', '2023-10-23 14:58:19');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429363, 10989164532800, 'shell01', 14, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:00:55', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:00:55');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429364, 10989164532800, 'shell01', 15, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:03:56', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:03:56');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429365, 10989164532800, 'shell01', 16, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:05:41', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:05:41');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429366, 10989164532800, 'shell01', 17, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:06:46', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:06:46');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429367, 10989164532800, 'shell01', 18, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:08:42', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:08:42');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429368, 10989164532800, 'shell01', 19, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:36:25', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:36:25');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429369, 10989164532800, 'shell01', 20, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:39:31', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:39:31');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429370, 10989164532800, 'shell01', 21, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:42:41', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:42:41');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429371, 10989164532800, 'shell01', 22, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:45:53', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:45:53');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429372, 10989164532800, 'shell01', 23, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 15:57:03', -1, -1, '2023-09-21 16:04:10', '2023-10-23 15:57:03');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429373, 10989164532800, 'shell01', 24, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 16:01:04', -1, -1, '2023-09-21 16:04:10', '2023-10-23 16:01:04');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429374, 10989164532800, 'shell01', 25, '', **************, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-10-23 16:02:50', -1, -1, '2023-09-21 16:04:10', '2023-10-23 16:02:50');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429375, 11421174732096, '001', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:34:36', -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:34:36');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429376, 11421191059392, '002', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:34:36', -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:34:36');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429377, 11421196238912, '123', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"234234\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:37:40', -1, -1, '2023-10-30 17:37:40', '2023-10-30 17:37:40');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429378, 11421228664000, '123123', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"123\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:39:16', -1, -1, '2023-10-30 17:39:16', '2023-10-30 17:39:16');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429379, 11421174732096, '001', 2, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:49:30', -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:49:30');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429380, 11421191059392, '002', 2, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"456\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:49:30', -1, -1, '2023-10-30 17:34:36', '2023-10-30 17:49:30');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429381, 11421313640256, '01', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:50:41', -1, -1, '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429382, 11421314732992, '002', 1, '', 11199696680000, '1', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"4561\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '1', NULL, 0, '2023-10-30 17:50:41', -1, -1, '2023-10-30 17:50:41', '2023-10-30 17:50:41');
INSERT INTO `t_ds_task_definition_log` VALUES (*************, **************, 'st_cluster_7d5', 1, '', **************, '1', 'SEATUNNEL', 0, '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true}', 1, 2, 'default', -1, 0, 1, 0, 0, 0, 0, '', '1', NULL, 0, '2023-10-30 18:06:15', -1, -1, '2023-10-30 18:06:15', '2023-10-30 18:06:15');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429384, 11508994849856, '测试调度01', 1, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 16:07:29', -1, -1, '2023-11-07 16:07:29', '2023-11-07 16:07:29');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429385, 11508994849856, '测试调度01', 2, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 16:21:27', -1, -1, '2023-11-07 16:07:29', '2023-11-07 16:21:27');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429386, 11508994849856, '测试调度01', 3, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000验证版本是否升级，当前是4\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 17:01:29', -1, -1, '2023-11-07 16:07:29', '2023-11-07 17:01:29');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429387, 11508994849856, '测试调度01', 4, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000测试05\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, 0, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 17:50:14', -1, -1, '2023-11-07 16:07:29', '2023-11-07 17:50:14');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429388, 11508994849856, '测试调度01', 5, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000测试06\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, 0, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 17:51:01', -1, -1, '2023-11-07 16:07:29', '2023-11-07 17:51:01');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429390, 11508994849856, '测试调度01', 6, '', **************, '10967307334208', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000测试07\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, 0, 0, 0, '', '10967307334208', NULL, 0, '2023-11-07 18:07:53', -1, -1, '2023-11-07 16:07:29', '2023-11-07 18:07:53');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429391, 11574435294368, '测试新增', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"xxxx\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 14:07:56', -1, -1, '2023-11-13 14:07:56', '2023-11-13 14:07:56');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429392, 11574566356256, '测试新增', 1, 'x', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{x}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 14:25:00', -1, -1, '2023-11-13 14:25:00', '2023-11-13 14:25:00');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429393, 11574629217824, '新增111测试', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 14:33:11', -1, -1, '2023-11-13 14:33:11', '2023-11-13 14:33:11');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429394, 11574629217824, '新增111测试', 2, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 14:51:24', -1, -1, '2023-11-13 14:33:11', '2023-11-13 14:51:24');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429395, 11574629217824, '新增111测试', 3, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:09:40', -1, -1, '2023-11-13 14:33:11', '2023-11-13 15:09:40');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429396, 11574629217824, '新增111测试', 4, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:25:56', -1, -1, '2023-11-13 14:33:11', '2023-11-13 15:25:56');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429397, 11574629217824, '新增111测试', 5, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:27:01', -1, -1, '2023-11-13 14:33:11', '2023-11-13 15:27:01');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429398, 11575060491168, '测试新增看工作流', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:29:21', -1, -1, '2023-11-13 15:29:21', '2023-11-13 15:29:21');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429399, 11575060491168, '测试新增看工作流', 2, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:29:47', -1, -1, '2023-11-13 15:29:47', '2023-11-13 15:29:47');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429400, 11575072923552, 'xxxx', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:30:58', -1, -1, '2023-11-13 15:30:58', '2023-11-13 15:30:58');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429401, 11574629217824, '新增111测试', 6, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:31:41', -1, -1, '2023-11-13 14:33:11', '2023-11-13 15:31:41');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429402, 11575124055840, '新增测试', 1, 'xx', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:37:37', -1, -1, '2023-11-13 15:37:37', '2023-11-13 15:37:37');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429403, 11574629217824, '新增111测试', 7, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:39:38', -1, -1, '2023-11-13 14:33:11', '2023-11-13 15:39:38');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429404, 11575146272416, '新增测试1', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:40:33', -1, -1, '2023-11-13 15:40:33', '2023-11-13 15:40:33');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429405, 11574629217830, '新增111测试', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:47:29', -1, -1, '2023-11-13 15:47:29', '2023-11-13 15:47:29');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429406, 11574629217830, '新增111测试', 2, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:48:42', -1, -1, '2023-11-13 15:47:29', '2023-11-13 15:48:42');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429407, 11574629217831, '新增111测试', 1, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:49:15', -1, -1, '2023-11-13 15:49:15', '2023-11-13 15:49:15');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429408, 11574629217831, '新增111测试', 2, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxx111}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:49:28', -1, -1, '2023-11-13 15:49:28', '2023-11-13 15:49:28');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429409, 11574629217831, '新增111测试', 3, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxxqqqq}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:54:38', -1, -1, '2023-11-13 15:54:38', '2023-11-13 15:54:38');
INSERT INTO `t_ds_task_definition_log` VALUES (7729538429410, 11574629217831, '新增111测试', 4, '', **************, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"{xxxqqqq111}\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', '4', NULL, 0, '2023-11-13 15:56:55', -1, -1, '2023-11-13 15:56:55', '2023-11-13 15:56:55');

-- ----------------------------
-- Table structure for t_ds_task_external_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_external_definition`;
CREATE TABLE `t_ds_task_external_definition`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `code` bigint(0) NOT NULL COMMENT 'encoding',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition name',
  `version` int(0) NULL DEFAULT 0 COMMENT 'task definition version',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `project_code` bigint(0) NOT NULL COMMENT 'project code',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task definition creator id',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int(0) NULL DEFAULT 0 COMMENT 'task execute type: 0-batch, 1-stream',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'job custom parameters',
  `flag` tinyint(0) NULL DEFAULT NULL COMMENT '0 not available, 1 available',
  `task_priority` tinyint(0) NULL DEFAULT 2 COMMENT 'job priority',
  `worker_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker grouping',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `fail_retry_times` int(0) NULL DEFAULT NULL COMMENT 'number of failed retries',
  `fail_retry_interval` int(0) NULL DEFAULT NULL COMMENT 'failed retry interval',
  `timeout_flag` tinyint(0) NULL DEFAULT 0 COMMENT 'timeout flag:0 close, 1 open',
  `timeout_notify_strategy` tinyint(0) NULL DEFAULT NULL COMMENT 'timeout notification policy: 0 warning, 1 fail',
  `timeout` int(0) NULL DEFAULT 0 COMMENT 'timeout length,unit: minute',
  `delay_time` int(0) NULL DEFAULT 0 COMMENT 'delay execution time,unit: minute',
  `resource_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'resource id, separated by comma',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task group id',
  `task_group_priority` tinyint(0) NULL DEFAULT 0 COMMENT 'task group priority',
  `cpu_quota` int(0) NOT NULL DEFAULT -1 COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int(0) NOT NULL DEFAULT -1 COMMENT 'MemoryMax(MB): -1:Infinity',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`, `code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '外部调度资源存储表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_external_definition
-- ----------------------------
INSERT INTO `t_ds_task_external_definition` VALUES (5, 10977953431872, '1111222311', 1, '', 0, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"1233334\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-20 17:00:09', '2023-09-21 11:35:17');
INSERT INTO `t_ds_task_external_definition` VALUES (6, 10977953431873, '1111222311', 1, '', 0, '4', 'SHELL', 0, '{\"localParams\":[],\"rawScript\":\"echo \\\"1233334\\\"\",\"resourceList\":[]}', 1, 2, 'default', -1, 0, 1, 0, NULL, 0, 0, '', NULL, 0, -1, -1, '2023-09-21 11:35:42', '2023-09-21 11:35:42');

-- ----------------------------
-- Table structure for t_ds_task_group
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_group`;
CREATE TABLE `t_ds_task_group`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task_group name',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `group_size` int(0) NOT NULL COMMENT 'group size',
  `use_size` int(0) NULL DEFAULT 0 COMMENT 'used size',
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'creator id',
  `project_code` bigint(0) NULL DEFAULT 0 COMMENT 'project code',
  `status` tinyint(0) NULL DEFAULT 1 COMMENT '0 not available, 1 available',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_group
-- ----------------------------
INSERT INTO `t_ds_task_group` VALUES ('11421285098560', '000', '', 10, 0, '1', **************, 1, '2023-10-30 17:46:30', '2023-10-30 17:46:30');
INSERT INTO `t_ds_task_group` VALUES ('11421291431488', '111', '', 10, 0, '1', 11199696680000, 1, '2023-10-30 17:47:19', '2023-10-30 17:47:19');

-- ----------------------------
-- Table structure for t_ds_task_group_queue
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_group_queue`;
CREATE TABLE `t_ds_task_group_queue`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'taskintanceid',
  `task_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'TaskInstance name',
  `group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'taskGroup id',
  `process_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'processInstace id',
  `priority` int(0) NULL DEFAULT 0 COMMENT 'priority',
  `status` tinyint(0) NULL DEFAULT -1 COMMENT '-1: waiting  1: running  2: finished',
  `force_start` tinyint(0) NULL DEFAULT 0 COMMENT 'is force start 0 NO ,1 YES',
  `in_queue` tinyint(0) NULL DEFAULT 0 COMMENT 'ready to get the queue by other task finish 0 NO ,1 YES',
  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_group_queue
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_task_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_task_instance`;
CREATE TABLE `t_ds_task_instance`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task name',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'task type',
  `task_execute_type` int(0) NULL DEFAULT 0 COMMENT 'task execute type: 0-batch, 1-stream',
  `task_code` bigint(0) NOT NULL COMMENT 'task definition code',
  `task_definition_version` int(0) NULL DEFAULT 0 COMMENT 'task definition version',
  `process_instance_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'process instance id',
  `state` tinyint(0) NULL DEFAULT NULL COMMENT 'Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete',
  `submit_time` datetime(0) NULL DEFAULT NULL COMMENT 'task submit time',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT 'task start time',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT 'task end time',
  `host` varchar(135) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'host of task running on',
  `execute_path` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task execute path in the host',
  `log_path` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'task log path',
  `alert_flag` tinyint(0) NULL DEFAULT NULL COMMENT 'whether alert',
  `retry_times` int(0) NULL DEFAULT 0 COMMENT 'task retry times',
  `pid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'pid of task',
  `app_link` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'yarn app id',
  `task_params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'job custom parameters',
  `flag` tinyint(0) NULL DEFAULT 1 COMMENT '0 not available, 1 available',
  `retry_interval` int(0) NULL DEFAULT NULL COMMENT 'retry interval when task failed ',
  `max_retry_times` int(0) NULL DEFAULT NULL COMMENT 'max retry times',
  `task_instance_priority` int(0) NULL DEFAULT NULL COMMENT 'task instance priority:0 Highest,1 High,2 Medium,3 Low,4 Lowest',
  `worker_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'worker group id',
  `environment_code` bigint(0) NULL DEFAULT -1 COMMENT 'environment code',
  `environment_config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'this config contains many environment variables config',
  `executor_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `first_submit_time` datetime(0) NULL DEFAULT NULL COMMENT 'task first submit time',
  `delay_time` int(0) NULL DEFAULT 0 COMMENT 'task delay execution time',
  `var_pool` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'var_pool',
  `task_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'task group id',
  `dry_run` tinyint(0) NULL DEFAULT 0 COMMENT 'dry run flag: 0 normal, 1 dry run',
  `cpu_quota` int(0) NOT NULL DEFAULT -1 COMMENT 'cpuQuota(%): -1:Infinity',
  `memory_max` int(0) NOT NULL DEFAULT -1 COMMENT 'MemoryMax(MB): -1:Infinity',
  `test_flag` tinyint(0) NULL DEFAULT NULL COMMENT 'test flag：0 normal, 1 test run',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `process_instance_id`(`process_instance_id`) USING BTREE,
  INDEX `idx_code_version`(`task_code`, `task_definition_version`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_task_instance
-- ----------------------------
INSERT INTO `t_ds_task_instance` VALUES ('10812047309248', '请求百度', 'HTTP', 0, 10811878809280, 1, '10812047232704', 7, '2023-09-05 15:38:40', '2023-09-05 15:38:40', '2023-09-05 15:38:40', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10811931125184_1/10812047232704/10812047309248', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10811931125184_1-10812047232704-10812047309248.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"https://www.baidu.com/sugrec?&prod=pc_his&from=pc_web&json=1&sid=39226_39278_39223_39285_39097_39261_39269_39240_39233_26350_39238_39224_39149&hisdata=&_t=1693898349736&csor=0\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 15:38:40', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812440570944', '自定义接口调试', 'HTTP', 0, 10812436419776, 1, '10812440500544', 6, '2023-09-05 16:29:52', '2023-09-05 16:29:52', '2023-09-05 16:29:52', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10812439524545_1/10812440500544/10812440570944', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_1-10812440500544-10812440570944.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:29:52', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812453429952', '自定义接口调试', 'HTTP', 0, 10812436419776, 2, '10812453365056', 6, '2023-09-05 16:31:32', '2023-09-05 16:31:33', '2023-09-05 16:31:33', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10812439524545_2/10812453365056/10812453429952', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_2-10812453365056-10812453429952.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:31:32', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812529906624', '自定义接口调试', 'HTTP', 0, 10812436419776, 3, '10812529819200', 7, '2023-09-05 16:41:30', '2023-09-05 16:41:30', '2023-09-05 16:41:40', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10812439524545_3/10812529819200/10812529906624', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_3-10812529819200-10812529906624.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:41:30', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812534954432', '自定义接口调试', 'HTTP', 0, 10812436419776, 3, '10812534885568', 9, '2023-09-05 16:42:09', '2023-09-05 16:42:09', '2023-09-05 16:42:19', '************:1234', NULL, 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_3-10812534885568-10812534954432.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 0, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:42:09', 0, NULL, NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812547568448', '自定义接口调试', 'HTTP', 0, 10812436419776, 3, '10812547434944', 9, '2023-09-05 16:43:48', '2023-09-05 16:43:48', '2023-09-05 16:43:54', '************:1234', NULL, 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_3-10812547434944-10812547568448.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:43:48', 0, NULL, NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812552411072', '自定义接口调试', 'HTTP', 0, 10812436419776, 3, '10812552296896', 9, '2023-09-05 16:44:26', '2023-09-05 16:44:26', '2023-09-05 16:44:31', '************:1234', NULL, 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812439524545_3-10812552296896-10812552411072.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 16:44:26', 0, NULL, NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10812681029056', 'shell测试', 'SHELL', 0, 10812663635136, 1, '10812680911296', 9, '2023-09-05 17:01:11', '2023-09-05 17:01:10', '2023-09-05 17:01:44', '************:1234', NULL, 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230905\\10812679584833_1-10812680911296-10812681029056.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"rawScript\":\"ping -t www.baidu.com \",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-05 17:01:11', 0, NULL, NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10821253156928', 'shell测试生成的文件', 'SHELL', 0, 10821244252096, 1, '10821253097408', 7, '2023-09-06 11:37:20', '2023-09-06 11:37:20', '2023-09-06 11:37:20', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10821251772608_1/10821253097408/10821253156928', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230906\\10821251772608_1-10821253097408-10821253156928.log', 0, 0, '8940', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-06 11:37:20', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10822332004544', 'shell测试', 'SHELL', 0, 10812663635136, 1, '10822331949760', 9, '2023-09-06 13:57:49', '2023-09-06 13:57:49', '2023-09-06 13:58:07', '***********:1234', NULL, 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230906\\10812679584833_1-10822331949760-10822332004544.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"rawScript\":\"ping -t www.baidu.com \",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-06 13:57:49', 0, NULL, NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10822358258496', '自定义接口调试', 'HTTP', 0, 10812436419776, 3, '10822358161728', 7, '2023-09-06 14:01:14', '2023-09-06 14:01:14', '2023-09-06 14:01:24', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10812439524545_3/10822358161728/10822358258496', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230906\\10812439524545_3-10822358161728-10822358258496.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"httpMethod\":\"GET\",\"httpCheckCondition\":\"STATUS_CODE_DEFAULT\",\"httpParams\":[],\"url\":\"http://127.0.0.1:5173/dolphinscheduler/test?time=10111&token=ee940593-3470-4413-916a-bb12c9a8b302\",\"condition\":\"\",\"connectTimeout\":60000,\"socketTimeout\":60000,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-06 14:01:14', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10835262097728', 'datax调度测试', 'DATAX', 0, 10835090078656, 1, '10835261995200', 6, '2023-09-07 18:01:25', '2023-09-07 18:01:25', '2023-09-07 18:02:05', '172.26.192.1:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10835095030848_1/10835261995200/10835262097728', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230907\\10835095030848_1-10835261995200-10835262097728.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"customConfig\":1,\"json\":\"json111\",\"xms\":1,\"xmx\":1,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-07 18:01:25', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10835269957312', 'datax调度测试', 'DATAX', 0, 10835090078656, 1, '10835269865536', 6, '2023-09-07 18:02:27', '2023-09-07 18:02:27', '2023-09-07 18:02:39', '172.26.192.1:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10835095030848_1/10835269865536/10835269957312', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230907\\10835095030848_1-10835269865536-10835269957312.log', 0, 0, NULL, NULL, '{\"localParams\":[],\"resourceList\":[],\"customConfig\":1,\"json\":\"json111\",\"xms\":1,\"xmx\":1,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-07 18:02:27', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10844661314240', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10844661257536', 7, '2023-09-08 14:25:17', '2023-09-08 14:25:16', '2023-09-08 14:25:16', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10844661257536/10844661314240', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230908\\10844574533184_1-10844661257536-10844661314240.log', 0, 0, '4280', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-08 14:25:17', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877723312064', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877723214912', 7, '2023-09-11 14:10:13', '2023-09-11 14:10:13', '2023-09-11 14:10:13', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877723214912/10877723312064', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877723214912-10877723312064.log', 0, 0, '12632', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:10:13', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877729576768', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877729436992', 7, '2023-09-11 14:11:02', '2023-09-11 14:11:02', '2023-09-11 14:11:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877729436992/10877729576768', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877729436992-10877729576768.log', 0, 0, '11912', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:11:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877754460096', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877754379712', 7, '2023-09-11 14:14:17', '2023-09-11 14:14:17', '2023-09-11 14:14:17', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877754379712/10877754460096', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877754379712-10877754460096.log', 0, 0, '12068', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:14:17', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877814484416', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877814399424', 7, '2023-09-11 14:22:06', '2023-09-11 14:22:06', '2023-09-11 14:22:06', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877814399424/10877814484416', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877814399424-10877814484416.log', 0, 0, '11808', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:22:06', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877821849792', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877821691072', 7, '2023-09-11 14:23:03', '2023-09-11 14:23:18', '2023-09-11 14:23:22', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877821691072/10877821849792', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877821691072-10877821849792.log', 0, 0, '8216', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:23:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877829400000', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877829333696', 7, '2023-09-11 14:24:02', '2023-09-11 14:24:02', '2023-09-11 14:24:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877829333696/10877829400000', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877829333696-10877829400000.log', 0, 0, '7812', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:24:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877921774144', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877921646912', 7, '2023-09-11 14:36:04', '2023-09-11 14:36:04', '2023-09-11 14:36:04', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877921646912/10877921774144', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877921646912-10877921774144.log', 0, 0, '7856', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:36:04', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877929202880', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877929134400', 7, '2023-09-11 14:37:02', '2023-09-11 14:37:02', '2023-09-11 14:37:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877929134400/10877929202880', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877929134400-10877929202880.log', 0, 0, '8228', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:37:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877936914752', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877936834752', 7, '2023-09-11 14:38:02', '2023-09-11 14:38:02', '2023-09-11 14:38:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877936834752/10877936914752', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877936834752-10877936914752.log', 0, 0, '13248', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:38:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877944971584', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877944780480', 7, '2023-09-11 14:39:05', '2023-09-11 14:39:05', '2023-09-11 14:39:05', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877944780480/10877944971584', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877944780480-10877944971584.log', 0, 0, '7724', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:39:05', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877952243520', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877952179264', 7, '2023-09-11 14:40:02', '2023-09-11 14:40:02', '2023-09-11 14:40:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877952179264/10877952243520', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877952179264-10877952243520.log', 0, 0, '6608', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:40:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877959893824', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877959840704', 7, '2023-09-11 14:41:02', '2023-09-11 14:41:02', '2023-09-11 14:41:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877959840704/10877959893824', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877959840704-10877959893824.log', 0, 0, '9816', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:41:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877967728576', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877967629632', 7, '2023-09-11 14:42:03', '2023-09-11 14:42:03', '2023-09-11 14:42:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877967629632/10877967728576', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877967629632-10877967728576.log', 0, 0, '7172', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:42:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877975304128', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877975210304', 7, '2023-09-11 14:43:02', '2023-09-11 14:43:02', '2023-09-11 14:43:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877975210304/10877975304128', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877975210304-10877975304128.log', 0, 0, '11540', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:43:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877982968384', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877982917184', 7, '2023-09-11 14:44:02', '2023-09-11 14:44:02', '2023-09-11 14:44:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877982917184/10877982968384', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877982917184-10877982968384.log', 0, 0, '10804', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:44:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877990796480', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877990581824', 7, '2023-09-11 14:45:03', '2023-09-11 14:45:03', '2023-09-11 14:45:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877990581824/10877990796480', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877990581824-10877990796480.log', 0, 0, '11404', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:45:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10877998343360', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10877998270528', 7, '2023-09-11 14:46:02', '2023-09-11 14:46:02', '2023-09-11 14:46:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10877998270528/10877998343360', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10877998270528-10877998343360.log', 0, 0, '7832', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:46:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878006186304', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878006107072', 7, '2023-09-11 14:47:03', '2023-09-11 14:47:03', '2023-09-11 14:47:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878006107072/10878006186304', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878006107072-10878006186304.log', 0, 0, '10512', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:47:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878013892544', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878013791424', 7, '2023-09-11 14:48:04', '2023-09-11 14:48:04', '2023-09-11 14:48:04', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878013791424/10878013892544', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878013791424-10878013892544.log', 0, 0, '6124', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:48:04', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878021337920', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878021236672', 7, '2023-09-11 14:49:02', '2023-09-11 14:49:02', '2023-09-11 14:49:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878021236672/10878021337920', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878021236672-10878021337920.log', 0, 0, '6088', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:49:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878029021248', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878028931520', 7, '2023-09-11 14:50:02', '2023-09-11 14:50:02', '2023-09-11 14:50:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878028931520/10878029021248', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878028931520-10878029021248.log', 0, 0, '5632', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:50:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878036831552', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878036744640', 7, '2023-09-11 14:51:03', '2023-09-11 14:51:03', '2023-09-11 14:51:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878036744640/10878036831552', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878036744640-10878036831552.log', 0, 0, '5856', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:51:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878044405696', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878044334016', 7, '2023-09-11 14:52:02', '2023-09-11 14:52:02', '2023-09-11 14:52:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878044334016/10878044405696', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878044334016-10878044405696.log', 0, 0, '5956', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:52:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878052092352', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878052053312', 7, '2023-09-11 14:53:02', '2023-09-11 14:53:02', '2023-09-11 14:53:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878052053312/10878052092352', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878052053312-10878052092352.log', 0, 0, '4968', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:53:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878059759552', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878059708608', 7, '2023-09-11 14:54:02', '2023-09-11 14:54:02', '2023-09-11 14:54:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878059708608/10878059759552', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878059708608-10878059759552.log', 0, 0, '4332', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:54:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878067432896', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878067353152', 7, '2023-09-11 14:55:02', '2023-09-11 14:55:02', '2023-09-11 14:55:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878067353152/10878067432896', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878067353152-10878067432896.log', 0, 0, '4484', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:55:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878075096000', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878075039552', 7, '2023-09-11 14:56:02', '2023-09-11 14:56:02', '2023-09-11 14:56:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878075039552/10878075096000', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878075039552-10878075096000.log', 0, 0, '10548', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:56:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878082827712', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878082715072', 7, '2023-09-11 14:57:02', '2023-09-11 14:57:02', '2023-09-11 14:57:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878082715072/10878082827712', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878082715072-10878082827712.log', 0, 0, '4272', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:57:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878090509504', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878090430784', 7, '2023-09-11 14:58:02', '2023-09-11 14:58:02', '2023-09-11 14:58:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878090430784/10878090509504', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878090430784-10878090509504.log', 0, 0, '7140', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:58:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878098242240', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878098129344', 7, '2023-09-11 14:59:03', '2023-09-11 14:59:02', '2023-09-11 14:59:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878098129344/10878098242240', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878098129344-10878098242240.log', 0, 0, '7048', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 14:59:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878105758400', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878105712192', 7, '2023-09-11 15:00:01', '2023-09-11 15:00:01', '2023-09-11 15:00:01', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878105712192/10878105758400', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878105712192-10878105758400.log', 0, 0, '5400', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:00:01', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878113564992', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878113511616', 7, '2023-09-11 15:01:02', '2023-09-11 15:01:02', '2023-09-11 15:01:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878113511616/10878113564992', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878113511616-10878113564992.log', 0, 0, '3712', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:01:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878121322688', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878121172288', 7, '2023-09-11 15:02:03', '2023-09-11 15:02:03', '2023-09-11 15:02:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878121172288/10878121322688', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878121172288-10878121322688.log', 0, 0, '3052', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:02:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878128852928', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878128782272', 7, '2023-09-11 15:03:02', '2023-09-11 15:03:01', '2023-09-11 15:03:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878128782272/10878128852928', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878128782272-10878128852928.log', 0, 0, '11836', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:03:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878136639936', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878136589120', 7, '2023-09-11 15:04:03', '2023-09-11 15:04:03', '2023-09-11 15:04:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878136589120/10878136639936', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878136589120-10878136639936.log', 0, 0, '9516', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:04:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878144336320', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878144243776', 7, '2023-09-11 15:05:03', '2023-09-11 15:05:03', '2023-09-11 15:05:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878144243776/10878144336320', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878144243776-10878144336320.log', 0, 0, '6096', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:05:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878152023232', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878151946560', 7, '2023-09-11 15:06:03', '2023-09-11 15:06:03', '2023-09-11 15:06:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878151946560/10878152023232', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878151946560-10878152023232.log', 0, 0, '5716', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:06:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878159703104', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878159649600', 7, '2023-09-11 15:07:03', '2023-09-11 15:07:03', '2023-09-11 15:07:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878159649600/10878159703104', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878159649600-10878159703104.log', 0, 0, '4520', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:07:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878167528384', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878167443008', 7, '2023-09-11 15:08:04', '2023-09-11 15:08:04', '2023-09-11 15:08:04', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878167443008/10878167528384', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878167443008-10878167528384.log', 0, 0, '12472', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:08:04', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878174934720', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878174866752', 7, '2023-09-11 15:09:02', '2023-09-11 15:09:01', '2023-09-11 15:09:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878174866752/10878174934720', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878174866752-10878174934720.log', 0, 0, '12196', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:09:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878182596672', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878182537536', 7, '2023-09-11 15:10:02', '2023-09-11 15:10:02', '2023-09-11 15:10:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878182537536/10878182596672', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878182537536-10878182596672.log', 0, 0, '12040', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:10:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878190278976', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878190200768', 7, '2023-09-11 15:11:02', '2023-09-11 15:11:01', '2023-09-11 15:11:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878190200768/10878190278976', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878190200768-10878190278976.log', 0, 0, '5548', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:11:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878197973312', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878197928128', 7, '2023-09-11 15:12:02', '2023-09-11 15:12:02', '2023-09-11 15:12:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878197928128/10878197973312', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878197928128-10878197973312.log', 0, 0, '5532', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 15:12:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878858663616', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878858574912', 7, '2023-09-11 16:38:03', '2023-09-11 16:38:03', '2023-09-11 16:38:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878858574912/10878858663616', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878858574912-10878858663616.log', 0, 0, '12380', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:38:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878866242880', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878866197312', 7, '2023-09-11 16:39:03', '2023-09-11 16:39:02', '2023-09-11 16:39:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878866197312/10878866242880', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878866197312-10878866242880.log', 0, 0, '13696', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:39:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878873849664', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878873790016', 7, '2023-09-11 16:40:02', '2023-09-11 16:40:02', '2023-09-11 16:40:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878873790016/10878873849664', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878873790016-10878873849664.log', 0, 0, '10288', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:40:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878881899968', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878881786688', 7, '2023-09-11 16:41:05', '2023-09-11 16:41:06', '2023-09-11 16:41:06', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878881786688/10878881899968', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878881786688-10878881899968.log', 0, 0, '13968', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:41:05', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878889203904', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878889151680', 7, '2023-09-11 16:42:02', '2023-09-11 16:42:02', '2023-09-11 16:42:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878889151680/10878889203904', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878889151680-10878889203904.log', 0, 0, '13736', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:42:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878896999488', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878896780608', 7, '2023-09-11 16:43:03', '2023-09-11 16:43:03', '2023-09-11 16:43:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878896780608/10878896999488', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878896780608-10878896999488.log', 0, 0, '12360', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:43:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878904676416', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878904538432', 7, '2023-09-11 16:44:03', '2023-09-11 16:44:03', '2023-09-11 16:44:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878904538432/10878904676416', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878904538432-10878904676416.log', 0, 0, '13384', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:44:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878912227264', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878912113728', 7, '2023-09-11 16:45:02', '2023-09-11 16:45:02', '2023-09-11 16:45:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878912113728/10878912227264', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878912113728-10878912227264.log', 0, 0, '13880', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:45:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878919912512', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878919795520', 7, '2023-09-11 16:46:02', '2023-09-11 16:46:02', '2023-09-11 16:46:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878919795520/10878919912512', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878919795520-10878919912512.log', 0, 0, '3736', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:46:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878927516096', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878927478336', 7, '2023-09-11 16:47:01', '2023-09-11 16:47:01', '2023-09-11 16:47:01', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878927478336/10878927516096', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878927478336-10878927516096.log', 0, 0, '13152', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:47:01', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878935413952', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878935318848', 7, '2023-09-11 16:48:03', '2023-09-11 16:48:03', '2023-09-11 16:48:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878935318848/10878935413952', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878935318848-10878935413952.log', 0, 0, '11672', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:48:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878943204416', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878942971840', 7, '2023-09-11 16:49:04', '2023-09-11 16:49:04', '2023-09-11 16:49:04', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878942971840/10878943204416', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878942971840-10878943204416.log', 0, 0, '11352', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:49:04', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878950963264', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878950781632', 7, '2023-09-11 16:50:04', '2023-09-11 16:50:04', '2023-09-11 16:50:05', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878950781632/10878950963264', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878950781632-10878950963264.log', 0, 0, '4460', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:50:04', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878958298688', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878958251200', 7, '2023-09-11 16:51:02', '2023-09-11 16:51:02', '2023-09-11 16:51:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878958251200/10878958298688', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878958251200-10878958298688.log', 0, 0, '4440', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:51:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878966169920', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878966008640', 7, '2023-09-11 16:52:03', '2023-09-11 16:52:05', '2023-09-11 16:52:05', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878966008640/10878966169920', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878966008640-10878966169920.log', 0, 0, '3944', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:52:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878973784768', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878973686592', 7, '2023-09-11 16:53:03', '2023-09-11 16:53:03', '2023-09-11 16:53:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878973686592/10878973784768', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878973686592-10878973784768.log', 0, 0, '3876', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:53:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878981458368', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878981380544', 7, '2023-09-11 16:54:03', '2023-09-11 16:54:03', '2023-09-11 16:54:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878981380544/10878981458368', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878981380544-10878981458368.log', 0, 0, '11316', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:54:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878989183552', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878988994112', 7, '2023-09-11 16:55:03', '2023-09-11 16:55:05', '2023-09-11 16:55:05', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878988994112/10878989183552', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878988994112-10878989183552.log', 0, 0, '13324', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:55:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10878996832192', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10878996766144', 7, '2023-09-11 16:56:03', '2023-09-11 16:56:03', '2023-09-11 16:56:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10878996766144/10878996832192', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10878996766144-10878996832192.log', 0, 0, '4360', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:56:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879004592576', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879004509120', 7, '2023-09-11 16:57:03', '2023-09-11 16:57:04', '2023-09-11 16:57:04', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879004509120/10879004592576', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879004509120-10879004592576.log', 0, 0, '10664', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:57:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879012121792', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879012032448', 7, '2023-09-11 16:58:02', '2023-09-11 16:58:02', '2023-09-11 16:58:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879012032448/10879012121792', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879012032448-10879012121792.log', 0, 0, '2944', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:58:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879019727296', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879019667904', 7, '2023-09-11 16:59:02', '2023-09-11 16:59:01', '2023-09-11 16:59:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879019667904/10879019727296', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879019667904-10879019727296.log', 0, 0, '10284', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 16:59:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879027458496', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879027383232', 7, '2023-09-11 17:00:02', '2023-09-11 17:00:02', '2023-09-11 17:00:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879027383232/10879027458496', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879027383232-10879027458496.log', 0, 0, '10192', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:00:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879035073216', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879035007680', 7, '2023-09-11 17:01:02', '2023-09-11 17:01:02', '2023-09-11 17:01:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879035007680/10879035073216', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879035007680-10879035073216.log', 0, 0, '8932', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:01:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879042926656', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879042850368', 7, '2023-09-11 17:02:03', '2023-09-11 17:02:03', '2023-09-11 17:02:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879042850368/10879042926656', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879042850368-10879042926656.log', 0, 0, '11792', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:02:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879050541760', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879050448320', 7, '2023-09-11 17:03:02', '2023-09-11 17:03:02', '2023-09-11 17:03:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879050448320/10879050541760', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879050448320-10879050541760.log', 0, 0, '9836', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:03:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879058183232', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879058077376', 7, '2023-09-11 17:04:02', '2023-09-11 17:04:02', '2023-09-11 17:04:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879058077376/10879058183232', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879058077376-10879058183232.log', 0, 0, '8796', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:04:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879065854272', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879065761216', 7, '2023-09-11 17:05:02', '2023-09-11 17:05:02', '2023-09-11 17:05:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879065761216/10879065854272', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879065761216-10879065854272.log', 0, 0, '2816', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:05:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879073619392', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879073541312', 7, '2023-09-11 17:06:03', '2023-09-11 17:06:03', '2023-09-11 17:06:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879073541312/10879073619392', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879073541312-10879073619392.log', 0, 0, '7792', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:06:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879081280064', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879081215168', 7, '2023-09-11 17:07:03', '2023-09-11 17:07:03', '2023-09-11 17:07:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879081215168/10879081280064', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879081215168-10879081280064.log', 0, 0, '3288', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:07:03', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879088956096', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879088863808', 7, '2023-09-11 17:08:02', '2023-09-11 17:08:02', '2023-09-11 17:08:03', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879088863808/10879088956096', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879088863808-10879088956096.log', 0, 0, '7432', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:08:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10879096563008', 'shell输出', 'SHELL', 0, 10844570462400, 1, '10879096501056', 7, '2023-09-11 17:09:02', '2023-09-11 17:09:02', '2023-09-11 17:09:02', '************:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10844574533184_1/10879096501056/10879096563008', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230911\\10844574533184_1-10879096501056-10879096563008.log', 0, 0, '11340', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"123\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-11 17:09:02', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('10989977964736', 'shell01', 'SHELL', 0, 10989164532800, 4, '10989977898304', 7, '2023-09-21 17:46:43', '2023-09-21 17:46:43', '2023-09-21 17:46:47', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10989552944256_3/10989977898304/10989977964736', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230921\\10989552944256_3-10989977898304-10989977964736.log', 0, 0, '9724', '', '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-21 17:46:43', 0, '[]', NULL, 0, -1, -1, 1);
INSERT INTO `t_ds_task_instance` VALUES ('10990000332864', 'shell01', 'SHELL', 0, 10989164532800, 5, '10990000279872', 7, '2023-09-21 17:49:38', '2023-09-21 17:49:37', '2023-09-21 17:49:41', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10989552944256_4/10990000279872/10990000332864', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230921\\10989552944256_4-10990000279872-10990000332864.log', 0, 0, '13108', '', '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-21 17:49:38', 0, '[]', NULL, 0, -1, -1, 1);
INSERT INTO `t_ds_task_instance` VALUES ('10990031694528', 'shell01', 'SHELL', 0, 10989164532800, 6, '10990031629760', 7, '2023-09-21 17:53:43', '2023-09-21 17:53:43', '2023-09-21 17:53:46', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10989552944256_5/10990031629760/10990031694528', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230921\\10989552944256_5-10990031629760-10990031694528.log', 0, 0, '9864', '', '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-21 17:53:43', 0, '[]', NULL, 0, -1, -1, 1);
INSERT INTO `t_ds_task_instance` VALUES ('10990047177280', 'shell01', 'SHELL', 0, 10989164532800, 7, '10990046844352', 7, '2023-09-21 17:55:44', '2023-09-21 17:55:43', '2023-09-21 17:55:47', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10989552944256_6/10990046844352/10990047177280', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230921\\10989552944256_6-10990046844352-10990047177280.log', 0, 0, '9708', '', '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-21 17:55:44', 0, '[]', NULL, 0, -1, -1, 1);
INSERT INTO `t_ds_task_instance` VALUES ('10990092718400', 'shell01', 'SHELL', 0, 10989164532800, 8, '10990092650048', 7, '2023-09-21 18:01:39', '2023-09-21 18:01:39', '2023-09-21 18:01:42', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/10989552944256_7/10990092650048/10990092718400', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20230921\\10989552944256_7-10990092650048-10990092718400.log', 0, 0, '10520', '', '{\"localParams\":[],\"rawScript\":\"ping www.baidu.com\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-09-21 18:01:39', 0, '[]', NULL, 0, -1, -1, 1);
INSERT INTO `t_ds_task_instance` VALUES ('**************', 'linux_st', 'SEATUNNEL', 0, **************, 1, '**************', 7, '2023-10-10 16:13:39', '2023-10-10 16:13:39', '2023-10-10 16:14:18', '**************:1234', '/tmp/dolphinscheduler/exec/process/**************/11199358291360_1/**************/**************', '/tmp/dolphinscheduler/worker-server/logs/********/11199358291360_1-**************-**************.log', 0, 0, '25892', '', '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-10-10 16:13:39', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('**************', 'linux_st', 'SEATUNNEL', 0, **************, 1, '**************', 7, '2023-10-10 16:31:22', '2023-10-10 16:31:24', '2023-10-10 16:32:15', '**************:1234', '/tmp/dolphinscheduler/exec/process/**************/11199358291360_1/**************/**************', '/tmp/dolphinscheduler/worker-server/logs/********/11199358291360_1-**************-**************.log', 0, 0, '26540', '', '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-10-10 16:31:22', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('**************', 'st_cluster', 'SEATUNNEL', 0, **************, 1, '**************', 7, '2023-10-10 16:41:59', '2023-10-10 16:41:58', '2023-10-10 16:42:36', '**************:1234', '/tmp/dolphinscheduler/exec/process/**************/11199601127968_1/**************/**************', '/tmp/dolphinscheduler/worker-server/logs/********/11199601127968_1-**************-**************.log', 0, 0, '26741', '', '{\"localParams\":[],\"rawScript\":\"env {\\n    \\\"job.mode\\\"=\\\"BATCH\\\"\\n    \\\"job.name\\\"=\\\"10\\\"\\n}\\nsource {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        query=\\\"SELECT `emp_id`, `account`, `age`, `nationality`, `insert_date` FROM `test`.`emp_quality`\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"snapshot.split.size\\\"=\\\"8096\\\"\\n        \\\"snapshot.fetch.size\\\"=\\\"1024\\\"\\n        \\\"incremental.parallelism\\\"=\\\"1\\\"\\n        \\\"connect.timeout.ms\\\"=\\\"30000\\\"\\n        \\\"connect.max-retries\\\"=\\\"3\\\"\\n        \\\"connection.pool.size\\\"=\\\"20\\\"\\n        \\\"chunk-key.even-distribution.factor.lower-bound\\\"=\\\"0.05\\\"\\n        \\\"chunk-key.even-distribution.factor.upper-bound\\\"=\\\"100\\\"\\n        \\\"sample-sharding.threshold\\\"=\\\"1000\\\"\\n        \\\"inverse-sampling.rate\\\"=\\\"1000\\\"\\n        \\\"startup.mode\\\"=\\\"INITIAL\\\"\\n        \\\"stop.mode\\\"=\\\"NEVER\\\"\\n    }\\n}\\ntransform {\\n}\\nsink {\\n    Jdbc {\\n        url=\\\"jdbc:mysql://**************:3306/bj_test\\\"\\n        driver=\\\"com.mysql.cj.jdbc.Driver\\\"\\n        user=\\\"root\\\"\\n        password=\\\"joyadata\\\"\\n        database=\\\"bj_test\\\"\\n        table=\\\"user\\\"\\n        \\\"connection_check_timeout_sec\\\"=\\\"30\\\"\\n        \\\"batch_size\\\"=\\\"1024\\\"\\n        \\\"is_exactly_once\\\"=\\\"false\\\"\\n        \\\"max_commit_attempts\\\"=\\\"3\\\"\\n        \\\"transaction_timeout_sec\\\"=\\\"-1\\\"\\n        \\\"max_retries\\\"=\\\"0\\\"\\n        \\\"auto_commit\\\"=\\\"true\\\"\\n        \\\"support_upsert_by_query_primary_key_exist\\\"=\\\"true\\\"\\n        \\\"generate_sink_sql\\\"=\\\"true\\\"\\n    }\\n}\\n\\n\",\"resourceList\":[],\"engine\":\"SEATUNNEL_ENGINE\",\"useCustom\":true,\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '4', '2023-10-10 16:41:59', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('11509093101760', '测试调度01', 'SHELL', 0, 11508994849856, 1, '11509093038656', 7, '2023-11-07 16:19:50', '2023-11-07 16:19:50', '2023-11-07 16:19:50', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/11508998330944_1/11509093038656/11509093101760', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20231107\\11508998330944_1-11509093038656-11509093101760.log', 0, 0, '11796', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 0, 1, 0, 2, 'default', -1, NULL, '10967307334208', '2023-11-07 16:19:50', 0, '[]', NULL, 0, -1, -1, 0);
INSERT INTO `t_ds_task_instance` VALUES ('11509910851648', '测试调度01', 'SHELL', 0, 11508994849856, 5, '11509910789824', 7, '2023-11-07 18:06:19', '2023-11-07 18:06:19', '2023-11-07 18:06:19', '***********:1234', 'd://tmp//dolphinscheduler/exec/process/**************/11508998330944_5/11509910789824/11509910851648', 'D:\\DSG\\git_repo\\cindasc\\dolphinscheduler\\logs\\20231107\\11508998330944_5-11509910789824-11509910851648.log', 0, 0, '9528', '', '{\"localParams\":[],\"rawScript\":\"echo \\\"12344444444444000测试06\\\"\",\"resourceList\":[],\"conditionResult\":\"null\",\"dependence\":\"null\",\"switchResult\":\"null\",\"waitStartTimeout\":null}', 1, 1, 0, 2, 'default', -1, NULL, '10967307334208', '2023-11-07 18:06:19', 0, '[]', NULL, 0, -1, -1, 0);

-- ----------------------------
-- Table structure for t_ds_tenant
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_tenant`;
CREATE TABLE `t_ds_tenant`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tenant_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'tenant code',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `queue_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'queue id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_tenant_code`(`tenant_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_tenant
-- ----------------------------
INSERT INTO `t_ds_tenant` VALUES ('3', 'Administrator', 'Administrator', '1', '2022-11-02 16:41:56', '2022-11-02 16:41:56');

-- ----------------------------
-- Table structure for t_ds_udfs
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_udfs`;
CREATE TABLE `t_ds_udfs`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'user id',
  `func_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'UDF function name',
  `class_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'class of udf',
  `type` tinyint(0) NOT NULL COMMENT 'Udf function type',
  `arg_types` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'arguments types',
  `database` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'data base',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `resource_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'resource id',
  `resource_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'resource name',
  `create_time` datetime(0) NOT NULL COMMENT 'create time',
  `update_time` datetime(0) NOT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_func_name`(`func_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_udfs
-- ----------------------------

-- ----------------------------
-- Table structure for t_ds_user
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_user`;
CREATE TABLE `t_ds_user`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `user_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'user name',
  `user_password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'user password',
  `user_type` tinyint(0) NULL DEFAULT NULL COMMENT 'user type, 0:administrator，1:ordinary user',
  `email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'email',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'phone',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'tenant id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `queue` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'queue',
  `state` tinyint(0) NULL DEFAULT 1 COMMENT 'state 0:disable 1:enable',
  `time_zone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'time zone',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_name_unique`(`user_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_user
-- ----------------------------
INSERT INTO `t_ds_user` VALUES ('1', 'admin', '7ad2410b2f4c074479a8937a28a22b8f', 0, '<EMAIL>', '', '3', '2022-10-13 10:18:04', '2022-10-13 10:18:04', NULL, 1, NULL);
INSERT INTO `t_ds_user` VALUES ('10967307334208', 'lihj01', '0192023a7bbd73250516f069df18b500', 1, '<EMAIL>', '', '3', '2023-09-19 16:34:49', '2023-09-19 16:34:49', 'default', 1, NULL);
INSERT INTO `t_ds_user` VALUES ('4', 'lihj', '0192023a7bbd73250516f069df18b500', 1, '<EMAIL>', '', '3', '2022-11-02 16:42:08', '2022-11-02 16:42:08', 'default', 1, NULL);

-- ----------------------------
-- Table structure for t_ds_version
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_version`;
CREATE TABLE `t_ds_version`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `version` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `version_UNIQUE`(`version`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'version' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_version
-- ----------------------------
INSERT INTO `t_ds_version` VALUES ('1', '3.1.0');

-- ----------------------------
-- Table structure for t_ds_worker_group
-- ----------------------------
DROP TABLE IF EXISTS `t_ds_worker_group`;
CREATE TABLE `t_ds_worker_group`  (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'worker group name',
  `addr_list` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'worker addr list. split by [,]',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create time',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT 'update time',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'description',
  `other_params_json` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'other params json',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name_unique`(`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_ds_worker_group
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
