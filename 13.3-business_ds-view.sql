-- 海豚修改 表成视图 
-- t_ds_user
DROP TABLE IF EXISTS `t_ds_user`;
DROP VIEW IF EXISTS `t_ds_user`;
-- 创建视图语句
CREATE VIEW t_ds_user AS
SELECT c.id                     AS id,
       c.username               AS user_name,
       c.`password`             AS user_password,
       '1'                      AS user_type,
       c.email                  AS email,
       c.phone                  AS phone,
       t.id                     AS tenant_id,
       c.create_time            AS create_time,
       c.last_modification_time AS update_time,
       'default'                AS queue,
       c.active                 AS state,
       ''                       AS time_zone
FROM joyadata.cms_user c
         LEFT JOIN
     joyadata.tms_tenant t ON c.tenant_code = t.code;


-- t_ds_tenant
DROP TABLE IF EXISTS `t_ds_tenant`;
DROP VIEW IF EXISTS `t_ds_tenant`;
-- 创建视图语句
CREATE VIEW t_ds_tenant AS
SELECT id                     AS id,
       code                   AS tenant_code,
       description            AS description,
       '1'                    AS queue_id,
       create_time            AS create_time,
       last_modification_time AS update_time
FROM joyadata.tms_tenant;

-- t_ds_project
DROP TABLE IF EXISTS `t_ds_project`;
DROP VIEW IF EXISTS `t_ds_project`;
-- 视图语句
CREATE VIEW t_ds_project AS
SELECT id                     AS id,
       name                   AS name,
       id                     AS code,
       description            AS description,
       create_by              AS user_id,
       '1'                    AS flag,
       create_time            AS create_time,
       last_modification_time AS update_time
FROM joyadata.tms_project;


--  t_ds_relation_project_user
DROP TABLE IF EXISTS `t_ds_relation_project_user`;
DROP VIEW IF EXISTS `t_ds_relation_project_user`;
-- 视图语句
CREATE VIEW t_ds_relation_project_user AS
SELECT id                     AS id,
       user_id                AS user_id,
       project_id             AS project_id,
       '7'                    AS perm,
       create_time            AS create_time,
       last_modification_time AS update_time
FROM joyadata.tms_project_user;