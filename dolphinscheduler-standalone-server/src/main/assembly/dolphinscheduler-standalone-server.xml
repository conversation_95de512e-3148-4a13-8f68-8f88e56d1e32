<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    <id>dolphinscheduler-standalone-server</id>
    <formats>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <baseDirectory>standalone-server</baseDirectory>
    <fileSets>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-alert/dolphinscheduler-alert-server/target/alert-server/libs</directory>
            <outputDirectory>libs/alert-server</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-api/target/api-server/libs</directory>
            <outputDirectory>libs/api-server</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-master/target/master-server/libs</directory>
            <outputDirectory>libs/master-server</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-worker/target/worker-server/libs</directory>
            <outputDirectory>libs/worker-server</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
                <include>*.yaml</include>
                <include>*.xml</include>
            </includes>
            <outputDirectory>conf</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>${basedir}/src/main/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
            <directoryMode>0755</directoryMode>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/dist-bin</directory>
            <outputDirectory>dist-bin</outputDirectory>
            <fileMode>0755</fileMode>
            <directoryMode>0755</directoryMode>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../script/env</directory>
            <outputDirectory>conf</outputDirectory>
            <includes>
                <include>dolphinscheduler_env.sh</include>
            </includes>
            <fileMode>0755</fileMode>
            <directoryMode>0755</directoryMode>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-dao/src/main/resources</directory>
            <includes>
                <include>sql/**/*</include>
            </includes>
            <outputDirectory>conf</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-common/src/main/resources</directory>
            <includes>
                <include>common.properties</include>
            </includes>
            <outputDirectory>conf</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-api/src/main/resources</directory>
            <includes>
                <include>task-type-config.yaml</include>
            </includes>
            <outputDirectory>conf</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../dolphinscheduler-ui/dist</directory>
            <outputDirectory>./ui</outputDirectory>
        </fileSet>
    </fileSets>

    <dependencySets>
        <dependencySet>
            <useTransitiveDependencies>false</useTransitiveDependencies>
            <outputDirectory>libs/standalone-server</outputDirectory>
        </dependencySet>
    </dependencySets>
</assembly>
