# Worker Group 自动管理解决方案

## 🎯 功能概述

实现了一个自动管理 `t_ds_worker_group` 表的功能：
- **Worker 上线**：自动在 `t_ds_worker_group` 表中添加记录
- **Worker 下线**：自动从 `t_ds_worker_group` 表中删除记录
- **实时同步**：定期检查 worker 状态变化，保持数据一致性

## 🏗️ 架构设计

### 核心组件

1. **WorkerGroupAutoManager** - 主要管理器
   - 监听 worker 状态变化
   - 自动维护 `t_ds_worker_group` 表
   - 定期检查任务（每30秒）

2. **JdbcOperator** - 数据库操作层
   - 新增 worker group 相关的 CRUD 操作
   - 查询注册中心中的 worker 数据

3. **WorkerGroupMapper** - MyBatis Mapper
   - 提供 `t_ds_worker_group` 表的数据库操作接口

4. **WorkerGroup** - 实体模型
   - 对应 `t_ds_worker_group` 表结构

## 📊 工作流程

### Worker 上线流程
```
1. Worker 启动 → 注册到注册中心
2. WorkerGroupAutoManager 检测到新 worker
3. 解析 worker 信息（地址、组名）
4. 更新或创建 t_ds_worker_group 记录
5. 记录日志：worker 上线事件
```

### Worker 下线流程
```
1. Worker 停止 → 从注册中心移除
2. WorkerGroupAutoManager 检测到 worker 消失
3. 从 worker group 地址列表中移除该 worker
4. 如果组内无其他 worker，删除整个组记录
5. 记录日志：worker 下线事件
```

### 状态检查流程
```
1. 每30秒执行一次检查任务
2. 查询注册中心中所有活跃的 worker
3. 对比缓存中的 worker 状态
4. 处理新上线和下线的 worker
5. 更新本地缓存
```

## 🔧 技术实现

### 关键代码结构

#### 1. WorkerGroupAutoManager
```java
public class WorkerGroupAutoManager implements AutoCloseable {
    // 缓存当前活跃的 worker 信息
    private final Map<String, String> activeWorkers = new ConcurrentHashMap<>();
    
    // 缓存当前的 worker group 信息
    private final Map<String, Set<String>> currentWorkerGroups = new ConcurrentHashMap<>();
    
    // 定期检查 worker 状态变化
    private void checkWorkerStatusChange() {
        // 1. 获取当前注册中心中的所有 worker
        // 2. 检查新上线的 worker
        // 3. 检查下线的 worker
        // 4. 更新缓存
    }
}
```

#### 2. JdbcOperator 扩展
```java
// 新增的 worker group 操作方法
public List<JdbcRegistryData> getWorkerRegistryData() throws SQLException
public boolean workerGroupExists(String groupName) throws SQLException
public void insertWorkerGroup(String groupName, String addrList, String description) throws SQLException
public void updateWorkerGroup(String groupName, String addrList) throws SQLException
public void deleteWorkerGroup(String groupName) throws SQLException
```

#### 3. WorkerGroupMapper
```java
@Mapper
public interface WorkerGroupMapper {
    @Insert("INSERT INTO t_ds_worker_group ...")
    int insert(WorkerGroup workerGroup);
    
    @Select("SELECT * FROM t_ds_worker_group WHERE name = #{name}")
    WorkerGroup selectByName(@Param("name") String name);
    
    @Update("UPDATE t_ds_worker_group SET addr_list = #{addrList} ...")
    int updateById(WorkerGroup workerGroup);
    
    @Delete("DELETE FROM t_ds_worker_group WHERE name = #{name}")
    int deleteByName(@Param("name") String name);
}
```

## 📋 数据库表结构

### t_ds_worker_group 表
```sql
CREATE TABLE `t_ds_worker_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT 'worker group name',
  `addr_list` text COMMENT 'worker addr list. split by [,]',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  `description` text COMMENT 'description',
  `other_params_json` text COMMENT 'other params json',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
```

## 🚀 部署和配置

### 1. 编译部署
```bash
# 编译 registry-mysql 模块
mvn clean compile -pl dolphinscheduler-registry/dolphinscheduler-registry-plugins/dolphinscheduler-registry-mysql

# 重启相关服务
systemctl restart dolphinscheduler-master
systemctl restart dolphinscheduler-worker
```

### 2. 配置要求
确保使用 MySQL 作为注册中心：
```yaml
registry:
  type: jdbc
  term-refresh-interval: 2s
  term-expire-times: 3
```

### 3. 验证功能
```sql
-- 查看自动创建的 worker group 记录
SELECT * FROM t_ds_worker_group WHERE description LIKE '%Auto-generated%';

-- 监控 worker group 变化
SELECT name, addr_list, create_time, update_time FROM t_ds_worker_group ORDER BY update_time DESC;
```

## 📊 监控和日志

### 关键日志信息
```
[INFO] Starting WorkerGroupAutoManager...
[INFO] 检测到 worker 上线: *************:1234 -> default
[INFO] 创建 worker group: default -> *************:1234
[INFO] 检测到 worker 下线: *************:1234 -> default
[INFO] 删除 worker group: default
```

### 监控指标
- Worker 上线/下线事件频率
- Worker group 记录数量变化
- 自动管理任务执行耗时
- 数据库操作成功率

## 🔍 故障排查

### 常见问题

1. **Worker group 记录未自动创建**
   - 检查 WorkerGroupAutoManager 是否正常启动
   - 确认 worker 是否成功注册到注册中心
   - 查看日志中的错误信息

2. **Worker 下线后记录未删除**
   - 检查心跳超时配置
   - 确认 worker 进程是否正常关闭
   - 查看定期检查任务是否正常执行

3. **数据库操作失败**
   - 检查数据库连接配置
   - 确认 `t_ds_worker_group` 表结构正确
   - 查看 SQL 执行错误日志

### 调试方法
```bash
# 查看 worker 注册信息
SELECT * FROM t_ds_jdbc_registry_data WHERE data_key LIKE '/dolphinscheduler/workers/%';

# 查看 worker group 自动管理日志
tail -f dolphinscheduler-master.log | grep "WorkerGroupAutoManager"

# 手动触发检查（重启服务）
systemctl restart dolphinscheduler-master
```

## 💡 扩展功能

### 可能的增强
1. **配置化管理**：支持通过配置文件控制自动管理行为
2. **事件通知**：worker 上线/下线时发送通知
3. **历史记录**：记录 worker group 变更历史
4. **健康检查**：定期验证 worker 的实际可用性
5. **负载均衡**：根据 worker 负载自动调整组配置

### 性能优化
1. **批量操作**：支持批量更新 worker group 记录
2. **缓存优化**：减少数据库查询频率
3. **异步处理**：使用异步方式处理状态变化
4. **增量同步**：只处理变化的 worker 信息

## 🎉 总结

这个解决方案实现了 worker group 的自动化管理，具有以下优势：

1. **自动化**：无需手动维护 worker group 记录
2. **实时性**：快速响应 worker 状态变化
3. **可靠性**：定期检查确保数据一致性
4. **可扩展**：易于添加新功能和优化
5. **可监控**：提供详细的日志和状态信息

通过这个功能，可以大大简化 worker group 的管理工作，提高系统的自动化程度和运维效率。
